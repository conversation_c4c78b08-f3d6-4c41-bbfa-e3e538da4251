<?php

// +----------------------------------------------------------------------
// | 滇医通
// +----------------------------------------------------------------------
// | Copyright (c) 2017 http://www.ynhdkc.com All rights reserved.
// +----------------------------------------------------------------------
// | Author: 黄生俊 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\common\service;

use app\api\controller\Patient;
use app\common\library\Wenjuan;
use app\common\service\HisBase;
use app\lib\A2Xml;
use pinyin\Pinyin;
use think\Db;
use app\common\model\AppointRecord;
use app\common\model\ExceptionLog;
use app\common\model\PatientCard;
use app\common\model\MzPayOrder;
use app\common\model\PromiseData;
use app\common\library\CacheManager;
use EasyWeChat\Foundation\Application;
use EasyWeChat\Message\Text;
use app\common\model\FullOrder;

/*
 * 儿童医院基础类
 */
class HosService extends HisBase {
    protected $hos_url = 'http://220.165.247.116:9090/HisApi.php'; // 滇医通前置机His地址
    protected $doctors_url = 'http://220.165.247.116:9090/HisGetDoctorsApi.php'; // 获取医生列表（专家排班的列表，科室医生的列表）
    protected $doctors_url_old = 'http://220.165.247.116:9090/HisGetDoctorsApi0722.php'; // 获取医生列表（专家排班的列表，科室医生的列表）
    protected $doctors_url_new = 'http://220.165.247.116:9090/HisGetDoctorsApi1217.php'; // 获取医生列表（专家排班的列表，科室医生的列表）
// //     protected $hos_url = 'http://220.165.247.118/HisApi.php'; // 滇医通前置机His地址
    protected $pacs_url = 'http://220.165.247.116:9090/PacsApi.php'; // 滇医通前置机His地址
    protected $lis_url = 'http://220.165.247.116:9090/LisApi.php'; // 滇医通前置机His地址
    protected $ml_url = 'http://220.165.247.116:9090/MlApi.php'; // 滇医通前置机His地址

    protected $mi_url = 'http://220.165.247.116:9090/MiApi.php'; // 滇医通前置机His地址

    /* protected $hos_url = 'http://42.243.109.34:9090/HisApi.php'; // 滇医通前置机His地址
     protected $doctors_url = 'http://42.243.109.34:9090/HisGetDoctorsApi.php'; // 获取医生列表（专家排班的列表，科室医生的列表）
//     protected $hos_url = 'http://220.165.247.118/HisApi.php'; // 滇医通前置机His地址
     protected $pacs_url = 'http://42.243.109.34:9090/PacsApi.php'; // 滇医通前置机His地址
     protected $lis_url = 'http://42.243.109.34:9090/LisApi.php'; // 滇医通前置机His地址
    protected $ml_url = 'http://42.243.109.34:9090/MlApi.php'; // 滇医通前置机His地址*/

    /*
     * 微信用
     * 测试
    ;微信支付接口9.7
    ;ServiceUrl=http://***********:8090/HISInterface_AllPurpose.asmx
    ;微信预约接口测试9.7
    ;ServiceUrl=http://***********:80/HISInterface_AllPurpose.asmx
            正式
    ;微信预约接口9.181
    ;ServiceUrl=http://*************:9990/HISInterface_AllPurpose.asmx
    ;微信支付接口9.181
    ;ServiceUrl=http://*************:9999/HISInterface_AllPurpose.asmx
    */
    // 测试接口
//      protected $bk_soap_url = 'http://***********/HISInterface_AllPurpose.asmx?wsdl';//滇医通：医院内网预约接口
//      protected $mz_soap_url = 'http://***********:8090/HISInterface_AllPurpose.asmx?wsdl';//滇医通医院内网门诊接口（新患者注册，卡号绑定）
//     protected $mzzf_soap_url = 'http://***********:9991/HISInterface_AllPurpose.asmx?wsdl';//滇医通，医院内网门诊支付
//     protected $zy_soap_url = 'http://***********:9992/HISInterface_AllPurpose.asmx?wsdl';//住院
    //微信用测试接口
//     protected $bk_soap_url = 'http://***********:80/HISInterface_AllPurpose.asmx?wsdl';//微信：医院内网预约接口
//     protected $mz_soap_url = 'http://***********:8090/HISInterface_AllPurpose.asmx?wsdl';//滇医通医院内网门诊接口（新患者注册，卡号绑定）
//     protected $mzzf_soap_url = 'http://***********:8090/HISInterface_AllPurpose.asmx?wsdl';//微信，支付测试接口医院内网门诊支付
    protected $pacs_soap_url = 'http://************:8099/GDInterface/services/hipApply?wsdl';//微信，检查报告

    // 正式接口(滇医通)
//     protected $bk_soap_url = 'http://*************:9995/HISInterface_AllPurpose.asmx?wsdl'; // 医院内网预约接口
//     protected $mz_soap_url = 'http://*************:9996/HISInterface_AllPurpose.asmx?wsdl'; // 医院内网门诊接口（新患者注册，卡号绑定）
//     protected $mzzf_soap_url = 'http://*************:9996/HISInterface_AllPurpose.asmx?wsdl'; // 门诊缴费正式接口医院内网门诊支付
    //正式接口（微信）
    protected $bk_soap_url = 'http://*************:9901/HISInterface_AllPurpose.asmx?wsdl';// 微信：医院内网预约接口
    protected $mz_soap_url = 'http://*************:9901/HISInterface_AllPurpose.asmx?wsdl';// 滇医通医院内网门诊接口（新患者注册，卡号绑定）

    protected $mi_test_soap_url = 'http://**************:30098/EaiServer';
    protected $mzzf_soap_url = 'http://*************:9901/HISInterface_AllPurpose.asmx?wsdl';// 微信，支付测试接口医院内网门诊支付
    protected $zy_soap_url = 'http://*************:9980/HISInterface_AllPurpose.asmx?wsdl';// 微信，支付测试接口医院内网门诊支付
    protected $lis_soap_url = 'http://*************:9904/LisReport.asmx?wsdl';// 微信，支付测试接口医院内网门诊支付
    protected $ml_soap_url = 'http://192.168.251.166:8089/PayService.asmx?wsdl';// 默联，支付测试接口医院内网门诊支付
    protected $ml_soap_url_ali = 'http://192.168.251.166:8090/PayService.asmx?wsdl';// 默联，支付阿里接口医院内网门诊支付
//     protected $bk_soap_url = 'http://*************:9990/HISInterface_AllPurpose.asmx?wsdl';// 微信：医院内网预约接口
//     protected $mz_soap_url = 'http://*************:9999/HISInterface_AllPurpose.asmx?wsdl';// 滇医通医院内网门诊接口（新患者注册，卡号绑定）
//     protected $mzzf_soap_url = 'http://*************:9999/HISInterface_AllPurpose.asmx?wsdl';// 微信，支付测试接口医院内网门诊支付


    protected $AccessKey = '95B94EDB1A0E0B20F603'; // 授权码
    protected $MiAccessKey = 'F36DE058D64FD74D3E220D75F463D1C2F99827A7C4C5D1C6DCED90055F574F4947527C04B32892178A'; //医保移动支付接口授权码
    protected $opera = '9999'; // 滇医通的是9996,微信操作员是9999
    protected $Organization = 1; // 机构编码
    protected $DeptNo; // 科室所属编号，1：前兴，2：树林，3：特需门诊
                         // 预约类型
    private $bookingtype = 1; // 滇医通是1，微信是17
    protected $rules = '预约5天之内的号源';
    protected static $DEV_OPENID = 'o9U-Ft38nV4fYay_1RqG-h7DT2WQ'; //开发者stel000
    //     protected static $DEV_OPENID = 'o9U-Ft-H6mB2pOYK7S7WUUwTxFZM'; // 开发者hsj
    //protected static $DEV_OPENID = 'o9U-Ft0voQiATFn4DSAmQt-jdFsE'; // 开发者wwt
//     protected static $DEV_OPENID = 'o9U-Ft-GWmrg3T0lu9SJH53mZij4'; // 开发者guoshuanghui
//    protected static $DEV_OPENID = 'o9U-FtwoNmsjbZ4pbRRSIPTBY-Jg'; // 开发者彭小万

    protected $ML_SECRET_KEY = ''; //默联平台门诊订单来源编号
    protected $hos_code_map = [
        '871002' => '1', // 前兴院区
        '871003' => '2', // 书林院区
        '871006' => '3' // 前兴-特需门诊
        ];
    protected $deptno_hoscode_map = [
        '1' => '871002', // 前兴院区
        '2' => '871003', // 书林院区
        '3' => '871006' // 前兴-特需门诊
        ];
    protected $deptname_hoscode_map = [
        '前兴' => '871002', // 前兴院区
        '书林' => '871003', // 书林院区
        '特需' => '871006' // 前兴-特需门诊
        ];
    public $hos_name_map = [
        '871002' => '昆明市儿童医院(前兴院区)', // 前兴院区
        '871003' => '昆明市儿童医院(书林院区)', // 书林院区
        '871006' => '昆明市儿童医院(前兴-特需门诊)' // 前兴-特需门诊
        ];
    // 默联平台相关配置
    protected $mlMerNo = [
        '871002' => 'E000099901', // 前兴院区
        '871003' => 'E000099902', // 书林院区
        '871006' => 'E000099901' // 前兴-特需门诊
    ];
    protected $mlZyMerNo = 'E000099903';
    protected $createBy = 'ETML';
    protected $signKey = [
        '871002' => 'R0EO76M5ONUO3ESCSCBZZ26QNP9NJ10U', // 前兴院区
        '871003' => 'AFAHTE0R642LQM4WRRKJTQBZS6FFVRS6', // 书林院区
        '871006' => 'R0EO76M5ONUO3ESCSCBZZ26QNP9NJ10U' // 前兴-特需门诊
    ];
    protected $mlZySignKey = 'R0EO76M5ONUO3ESCSCBZZ26QNP9NJ10U';
    // 住院阿里
    /**
    E000099903 前兴院区住院 J7IRBIKS7903R9903E29XQIB99G4FY92
    E000099904 书林院区住院 MRTIBA3OUK7OWGUJNVZH7P5QP5RNMXGQ   这个是住院的
     */
    protected $mlAliZyMerNo = 'E000099903';
    protected $mlAliZySignKey = 'J7IRBIKS7903R9903E29XQIB99G4FY92';

    // 门诊接口方法
    static $MZ_Method = array(
        'MZ_GETIDCard', // 同步id card number
        'BK_GetPatInfo', // 获取就诊人信息
        'MZ_RegisterPat', // 就诊人注册
        'MZ_RegisterPat_test', // 就诊人注册测试
        'MZ_BingCard', // 就诊人绑定校验
        'LIS_GetReportList', // 获取检验报告结果
        'LIS_GetReportDetail', // 获取检验报告结果
        'mz_patient_vuid',
        'GetJYItem', // 2.5.1获取开单项目（查询字典信息）
        'InsertDetailHS', // 2.5.2InsertDetailHS开核酸项目
        'InsertDetailHS_9253', // 2.5.2InsertDetailHS开核酸项目
        'SethmdSys', // 信用就医
        'GetPopulation', //待诊人数查询
        'Mz_getNetUrl',
        'MZ_GetGuideInfo'
//        'Mz_HuaJia' //门诊医保划价
//        'HqWxPayService'
        );
    // LIS报告接口方法
    static $LIS_Method = array(
        'GetPatientsInfo', // 获取检验报告结果列表
        'GetResultInfo' // 获取检验报告结果
        );

    // 预约接口方法
    static $YY_Method = array(
        'BK_GetOrgDept', // 获取科室
        'BK_GetSchedule', // 获取排班
        'BK_GetSchedule_Test', // 获取排班
        'BK_GetProSchedule', // 获取专家排班
        'BK_GetProSchedule_Test', // 获取专家排班
        'BK_GetDoctorList', // 获取医生列表
        'BK_DoBooking', // 预约挂号
        'BK_DoBooking2024', // 预约挂号测试接口2024
        'BK_DoCancleBook', // 撤销预约
        'BK_GetDistInfo', // 获取分诊排队信息
        'MZ_GetBookRecord' // 获取预约记录
        );
    // 支付接口方法
    static $ZF_Method = array(
        'MZ_GetUnPayListDYT', // 获取待缴费清单
        'MZ_GetUnPayList', // 获取待缴费清单
        'MZ_PaymentDYT', // 门诊支付
        'MZ_Payment', // 门诊支付
        'MZ_GetPayStatus', // 获取已缴费清单列表
        'MZ_GetPayListDYT', // 获取已缴费清单列表
        'MZ_GetPayList', // 获取已缴费清单列表
        'MzErOrder', // 获取门诊缴费信息
        'MzRefundOrder', // 获取门诊需要退费的订单
        'MzChuFangCX', // 获取门诊处方详情
        'MZ_GetPayList2024',
        'MZ_GetUnPayList2024',
        'MZ_Payment2024',
        'MzChuFangCX2024',
        'Mz_getPatientID',
        'Mz_getMsg',
    );
    // 住院接口方法
    static $ZY_Method = array(
        'ZyHospitalRecords', //住院记录
        'ZY_GetPatChargeInfo', //住院费用信息
        'ZyDepositMin', // 住院预交金费用低于500患者信息查询
        'ZyPayDeposit', // 住院充值
        'ZyErOrder' // 住院充值异常订单核查
    );
    protected $tx_payamt = 0.1; // 特需支付费用
    public function __construct($hos_code = '871002') {
        if (empty($hos_code)) $hos_code = '871002';
        if (! isset($this->hos_code_map[$hos_code])) {
            throw new \think\Exception("错误的医院编码");
            // $this->returnErorr("错误的医院编码");
        }
        $this->hos_code = $hos_code;
    }

    protected function http_request_xml($url,$data = null,$arr_header = null, $timeout=10){
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
        // curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
        // curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
        if (!empty($data)){
            curl_setopt($curl, CURLOPT_POST, 1);
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        }
        if(!empty($arr_header)){
            curl_setopt($curl, CURLOPT_HTTPHEADER, $arr_header);
        }
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        $output = curl_exec($curl);
        curl_close($curl);
        return $output;
    }

    function build_soap_body($method, $param, $namespacekey, $namespaceurl) {
        $body = "";
        foreach ($param as $key => $value) {
            $body .= "<{$namespacekey}:{$key}>{$value}</{$namespacekey}:{$key}>";
        }
        $xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?><soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope\/\" xmlns:{$namespacekey}=\"{$namespaceurl}\">
        <soapenv:Header/>
        <soapenv:Body>
        <soapenv:Body>
        <{$namespacekey}:{$method}>
        <!--Optional:-->
        {$body}
        </{$namespacekey}:{$method}>
        </soapenv:Body>
        </soapenv:Envelope>";
        return $xml;
    }


    function build_soap_head($method,$data, $namespaceurl) {
        $arr_header = [];
        $arr_header[] = "Content-Type: application/soap+xml; charset=utf-8"; //使用xml格式传递
        $arr_header[] = "Content-length:".strlen($data);
        $arr_header[] = "SOAPAction:\"{$namespaceurl}:{$method}\""; //添加swdl的方法
        return $arr_header;
    }

    public function pushPayOrderMl($mzOrder)
    {
//        dump($mzOrder);die;
        $Request['MerBillNo'] = $mzOrder['order_no'];
        $Request['MerNo'] = $this->mlMerNo[$mzOrder['hos_id']];
        $Request['OrderDate'] = date("Y-m-d");
        $Request['OrderAmount'] = $mzOrder['total_price'];
        $Request['OrderSource'] = '04';
        $Request['OrderSourceCode'] = '04';
        $Request['ProductName'] = '处方';
        $Request['CreateBy'] = $this->createBy;
        $Request['OrderType'] = '02';
        $Request['SourceId'] = $mzOrder['jz_card'];
        $Request['SignType'] = '01';
        $signContent = strtoupper($Request['MerBillNo'].'|'.$Request['MerNo'].
            '|'.$Request['OrderDate'].'|'.$Request['OrderAmount'].'|'.
            '|'.$Request['OrderSource'].'|'.$Request['OrderSourceCode'].'|'.$this->signKey[$mzOrder['hos_id']]);
        $Request['SignData'] = md5($signContent);
        $DataTable['data'] = $Request;
        $xml = new A2Xml();
        return $xml->toXml($DataTable);
    }


    // $from 如果是微信可不传，如果是支付宝传1
    function callMlPost($params,$from=0){
        $params['soap_url'] = $this->ml_soap_url;
        if($from == 1){
            $params['soap_url'] = $this->ml_soap_url_ali;
        }


        debug('hisStart');
        $res = http_post_request($this->ml_url, $params);
        debug('hisEnd');

        $apiruntime = debug('hisStart','hisEnd',6);
        if (intval($apiruntime) > 5) {
            if (isset($result['runtime'])) {
                system_log('调用儿童医院his接口:MethodName=' . $params['funCode'] . ' 时间：' . $apiruntime.'s  HIS执行时间：' . $res['runtime'].'s  参数：'.json_encode($params). ' 返回结果：'.json_encode($res));
            } else {
                system_log('调用儿童医院his接口:MethodName=' . $params['funCode'] . ' 调用时间：' . $apiruntime.'s  参数：'.json_encode($params). ' 返回结果：'.json_encode($res));
            }
        }
        return  json_decode($res, true);
    }

    // 默联门诊支付改造(支付宝小程序)   推送缴费订单(F000102)
    function getMzAliPayId($options = array()){
        if($options['status']==1){
            return $this->returnErorr('该订单已支付');
        }

        if(!empty($options['his_order_no'])){
            $res['data']['OrderNo'] = $options['his_order_no'];
        } else{
//            $paramsXml = $this->pushPayOrderMl($options);
            // 调用   推送缴费订单(F000102) 推送缴费订单(F000102)
            $Request['MerBillNo'] = $options['order_no'];
            $Request['MerNo'] = $this->mlMerNo[$options['hos_id']];
            $Request['OrderDate'] = date("Y-m-d");
            $Request['OrderAmount'] = $options['total_price'];
            $Request['OrderSource'] = '05';
            $Request['OrderSourceCode'] = '04';
            $Request['ProductName'] = '处方';
            $Request['CreateBy'] = $this->createBy;
            $Request['OrderType'] = '02';
            $Request['SourceId'] = $options['jz_card'];
            $Request['SignType'] = '01';
            $signContent = strtoupper($Request['MerBillNo'].'|'.$Request['MerNo'].
                '|'.$Request['OrderDate'].'|'.$Request['OrderAmount'].'|'.
                '|'.$Request['OrderSource'].'|'.$Request['OrderSourceCode'].'|'.$this->signKey[$options['hos_id']]);
            $Request['SignData'] = md5($signContent);
            $DataTable['data'] = $Request;
            $xml = new A2Xml();
            $paramsXml =  $xml->toXml($DataTable);
//        dump($paramsXml);die();
            $params['xml'] = $paramsXml;
//        $data['funCode'] = 'F000102';
            $params['funCode'] = 'PushOrderInfo_Pay_V3_Nodetail';
            $res = $this->callMlPost($params,1);

            // 判断前置机返回前端 0 表示失败
            if($res['code'] != 1) return $res;


            // 判断默联是否有返回值
            if(empty($res['data'])){
                return ['code' => 0,'msg' => '默联支付宝推送缴费订单接口 F000102 未返回任何数据'];
            }
//            dump($options);
            // 判断默联是否有预期的返回值
            if($res['data']['RspCode'] != 0){
                return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
            }

            // 记录 默联 订单号
            $resDb = Db::name('mz_pay_order')->where(['order_no'=>$options['order_no']])->update(['his_order_no'=>$res['data']['OrderNo']]);
            if(!$resDb){
                system_log('单号:'.$options['order_no'].'的订单更新默联单号:'.$res['data']['OrderNo'].'失败');
            }
        }

        $tmp = Db::name('mz_pay_order_ext')->where(['id'=>$options['order_id']])->find();
        if(!$tmp){
            $res1 = $this->createMzMlAliPay($options);
            if($res1['code'] != 1) return $res1;
            if(empty($res1['data'])){
                return ['code' => 0,'msg' => '默联支付宝下单接口 F000225 未返回任何数据'];
            }
            // 判断默联是否有预期的返回值
            if($res1['data']['RspCode'] != 0){
                return ['code'=> 0, 'msg'=>isset($res1['data']['RspCode']) ? $res1['data']['RspMsg'] : '未知的错误'];
            }

            // 默联创建成功保存下
            $tdata['id'] = $options['order_id'];
            $tdata['tran_no'] = $res1['data']['TranNo'];
            $tdata['trade_no'] = $res1['data']['TradeNO'];
            $tdata['hos_id'] = $options['hos_id'];
            $tdata['openid'] = $options['openid'];
            Db::name('mz_pay_order_ext')->insert($tdata);
            $tmp['tran_no'] = $tdata['tran_no'];
            $tmp['trade_no'] = $tdata['trade_no'];
        }
        // 返回前端数据
        $res['data']['MerNo'] = $this->mlMerNo[$options['hos_id']];
        $res['data']['OrderDate'] = date('Y-m-d');
        $res['data']['OrderAmount'] = $options['total_price'];
        $res['data']['openid'] = $options['openid'];
        $res['data']['order_id'] = $options['order_id'];
        $res['data']['TranNo'] = $tmp['tran_no'];
        $res['data']['TradeNO'] = $tmp['trade_no'];
        return ['code'=>1,'msg'=>'获取成功','data'=>$res['data']];
    }

    // 默联门诊支付改造(支付宝小程序)   支付宝下单(F000225)
    const ALIPAYOPID = "09933";
    function createMzMlAliPay($options = array()){
        $Request['MerBillNo'] = $options['order_no'];
        $Request['Amount'] = $options['total_price'];
        $Request['ChannelCode'] = 'ALIPAY_APPLETS';
        $Request['BuyerId'] = $options['openid'];
        $Request['AppName'] = '';
        $Request['OperUser'] = self::ALIPAYOPID; // 待确认
        $Request['SignType'] = '01';
        $signContent = strtoupper($Request['MerBillNo'].'|'.$Request['Amount'].
            '|'.$Request['ChannelCode'].'|'.$this->signKey[$options['hos_id']]);
        $Request['SignData'] = md5($signContent);
        $Request['NotifyUrl'] = '';
        $Request['ReturnUrl'] = '';
        $DataTable['data'] = $Request;
        $xml = new A2Xml();
        $paramsXml =  $xml->toXml($DataTable);
//        dump($paramsXml);die();
        $params['xml'] = $paramsXml;
//        $data['funCode'] = 'F000225';
        $params['funCode'] = 'UnifiedOrderForAliPay';
        system_log("alipay:通过默联进行支付宝下单,req:".json_encode($params));
        $res = $this->callMlPost($params,1);
        system_log("alipay:通过默联进行支付宝下单,res:".json_encode($res['data']));
        return $res;
    }

    /**
     * 默联返回数据
     * ["RspCode"] => string(1) "0"  // 0:成功；1:不存在；-1:异常
    ["RspMsg"] => array(0) {}
    ["MerBillNo"] => string(20) "MZ221108893677475286" // 商户订单号
    ["OrderNo"] => string(20) "R2022111000008371739" // 支付平台返回订单号
    ["OrderType"] => string(2) "02" // 01:挂号 02:处方 04:门诊充值 05:预约挂号 06:住院充值 08:检查订单 09:检验
    ["OrderDate"] => string(10) "2022-11-10" // 订单日期
    ["OrderAmount"] => string(1) "4" // 订单金额
    ["OrderState"] => string(2) "02" //订单主状态（整个订单状态） 01 未支付   02已支付   05 退费
    ["TranState"] => string(2) "02" // 交易状态（单笔通道状态
    ["SourseId"] => string(8) "********" // 患者唯一号
    ["ChannelTranNo"] => string(28) "2022111022001453991410659471" // 通道交易流水号
    ["ChannelCode"] => string(14) "ALIPAY_APPLETS"
    ["RealCost"] => string(1) "4" // 实际交易金额
    ["TOTCost"] => string(1) "4" // 通道需支付金额
    ["BankOrgTraceNum"] => array(0) {}
    ["BankOrgTranDate"] => array(0) {}
    ["MarkingTransNo"] => array(0) { }
     */
    function getMzAliPayQuery($options = array()){
        $Request['TranNo'] = $options['tran_no'];
        $Request['SignType'] = '01';
        $signContent = strtoupper($Request['TranNo'].'|'.$this->signKey[$options['hos_id']]);
        $Request['SignData'] = md5($signContent);
        $DataTable['data'] = $Request;
        $xml = new A2Xml();
        $paramsXml =  $xml->toXml($DataTable);
        $params['xml'] = $paramsXml;
//        $data['funCode'] = 'F00010602';
        $params['funCode'] = 'QueryOrderTrans';
        system_log("查询默联接口 F00010602 是否支付，请求参数".json_encode($params));
        $res = $this->callMlPost($params,1);
        system_log("查询默联接口 F00010602 是否支付，出参".json_encode($res));
        if($res['code'] != 1) return $res;
        if(empty($res['data'])){
            return ['code' => 0,'msg' => '默联订单查询接口 F00010602 未返回任何数据'];
        }
        // 判断默联是否有预期的返回值
        if($res['data']['RspCode'] != 0){
            return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
        }
        if(!isset($res['data']['OrderState']) || $res['data']['OrderState'] != '02'){
            return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
        }
        if(!isset($res['data']['TranState']) || $res['data']['TranState'] != '02'){
            return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
        }
//        dump($options);
        // 留个结果
        $resCheck = $this->cheackAliMlPayOrder($res['data'],$options['openid'],$options['tran_no'],1);
//        $resCheck = true;
        // true 通知his   false 已经通知
        if($resCheck){
            //TODO MI test
            if ($options['jz_card'] == '17339991') {
                $Request_his['MethodName'] = 'MZ_Payment2024';
            } else{
                $Request_his['MethodName'] = 'MZ_Payment';
            }
            $Request_his['PatientID'] = $options['jz_card'];
            $Request_his['AdmissTimes'] = $options['bill_id'];
            $Request_his['PayID'] = $options['bill_id'];
            $Request_his['PayType'] = 'd'; // 与医院人员确认支付类型
            $Request_his['TransactionID'] = $options['tran_no'];
            $Request_his['OutTradeNo'] = $options['tran_no'];
            $Request_his['ChargeTotal'] =  sprintf("%.2f",$res['data']['OrderAmount']);
//             dump($Request_his);die;
            system_log('开始调用昆明市儿童医院his接口,支付订单:' . json_encode($Request_his));
            $res_his = $this->call($Request_his);
            // $res['Result']=1;
            system_log("调用his门诊支付接口 返回：".json_encode($res_his));
            if (isset($res_his['Result']) && $res_his['Result'] == 1) {
                // 修改数据库状态
                $resMsg = isset($res['Error']) ? $res['Error'] : "支付成功";
                $queue_sn = isset($res['Queue_sn']) ? $res['Queue_sn'] : 0; // 排队序号
                $data['id'] = $options['id'];
                $data['queue_sn'] = $queue_sn;
                $data['res_msg'] = $resMsg;
                $data['update_time'] = time();
                $data['status'] = 1; // 支付成功
                system_log(date("Y-m-d H:i:s")." 调用his接口支付成功,开始更新数据库状态");
                $payOrderModel = new MzPayOrder();
//                $updateResult = $payOrderModel->updateMzRecordOrder($data);
                $payOrderModel->updateMzRecordOrder($data);
                // 将支付信息再次通知默联
                try {
                    $RequestMl['OrderNo'] = $options['his_order_no'];
                    $RequestMl['Hislsh'] = $options['tran_no'];
                    $DataTableMl['data'] = $RequestMl;
                    $xml = new A2Xml();
                    $paramsMl['xml'] = $xml->toXml($DataTableMl);
                    // $data['funCode'] = 'F000120';
                    $paramsMl['funCode'] = 'UpdateOrderInfoForLSH';
                    system_log("儿童门诊缴费,通知默联请求:".json_encode($paramsMl));
                    $resMl = $this->callMlPost($paramsMl,1);
                    system_log("儿童门诊缴费,通知默联结果:".json_encode($resMl));
                } catch (\Exception $e) {
                    system_log("默联通知出现异常".$e->getMessage());
                }
                return self::returnSuccess([],'全流程执行完成');
            }else{
                return self::returnErorr("回写his门诊支付数据失败");
            }
        }
        // 返回前段成功
        return self::returnSuccess([],"已经通知his与默联");
    }

    // false 已经通知his
    function cheackAliMlPayOrder($molianNotify,$openid,$tradeNo,$type){
        $order_no = $molianNotify['MerBillNo'];
        // 可以不写了，还是留个记录
        $payOrder = Db::name('pay_order')->where(['order_no' => $order_no])->find();
        if (!$payOrder) { // 如果没有记录,将支付信息写入微信支付订单表
            $WxPayOrderData['mch_id'] = '2088021101787229'; //
            $WxPayOrderData['openid'] = $openid; // 用户openid
            $WxPayOrderData['sub_mch_id'] =  ''; // 子商户ID
            $WxPayOrderData['total_price'] = isset($molianNotify['OrderAmount']) ? $molianNotify['OrderAmount'] : 0;
            $WxPayOrderData['real_pay_money'] = isset($molianNotify['OrderAmount']) ? $molianNotify['OrderAmount'] : 0;
            $WxPayOrderData['order_no'] = $order_no;
            $WxPayOrderData['transaction_id'] = $tradeNo;
            $WxPayOrderData['pay_time'] = time();
            $WxPayOrderData['type'] = $type; // 0-挂号缴费,1-门诊缴费,2-门诊充值,3-住院充值
            $WxPayOrderData['status'] = 1;
            Db::name('pay_order')->insert($WxPayOrderData);
            return true;
        }
        return false;
    }



    // 默联门诊支付改造
    function getMzWxPayId($options = array()){
        if($options['status']==1){
            return $this->returnErorr('该订单已支付');
        }

        if(!empty($options['his_order_no'])){
            $res['data']['OrderNo'] = $options['his_order_no'];
        } else{
            $paramsXml = $this->pushPayOrderMl($options);
//        dump($paramsXml);die();
            $params['xml'] = $paramsXml;
//        $data['funCode'] = 'F000102';
            $params['funCode'] = 'PushOrderInfo_Pay_V3_Nodetail';
            $res = $this->callMlPost($params);

            // 判断前置机返回前端 0 表示失败
            if($res['code'] != 1) return $res;


            // 判断默联是否有返回值
            if(empty($res['data'])){
                return ['code' => 0,'msg' => 'his接口未返回任何数据'];
            }
//            dump($options);
            // 判断默联是否有预期的返回值
            if($res['data']['RspCode'] != 0){
                return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
            }

            // 记录 默联 订单号
            $resDb = Db::name('mz_pay_order')->where(['order_no'=>$options['order_no']])->update(['his_order_no'=>$res['data']['OrderNo']]);
            if(!$resDb){
                system_log('单号:'.$options['order_no'].'的订单更新默联单号:'.$res['data']['OrderNo'].'失败');
            }
        }



        $res['data']['MerNo'] = $this->mlMerNo[$options['hos_id']];
        $res['data']['OrderDate'] = date('Y-m-d');
        $res['data']['OrderAmount'] = $options['total_price'];
        $res['data']['SignMD5'] = md5($options['order_no'].'|'.date('Y-m-d').'|'.$options['total_price'].'|'.$options['jz_card'].'|'.$this->signKey[$options['hos_id']]);
        $notifyUrl = url('api/mz/paynotifyml',[],false,true);//默认的通知地址
        $res['data']['Merchanturl'] = '';
        $res['data']['S2SMerchanturl'] = $notifyUrl;
        $res['data']['openid'] = $options['openid'];
        $res['data']['order_id'] = $options['order_id'];

        return ['code'=>1,'msg'=>'获取成功','data'=>$res['data']];
    }


    function getZyAliPayQuery($options = array()){
        $Request['TranNo'] = $options['tran_no'];
        $Request['SignType'] = '01';
        $signContent = strtoupper($Request['TranNo'].'|'.$this->signKey[$options['hos_id']]);
        $Request['SignData'] = md5($signContent);
        $DataTable['data'] = $Request;
        $xml = new A2Xml();
        $paramsXml =  $xml->toXml($DataTable);
        $params['xml'] = $paramsXml;
//        $data['funCode'] = 'F00010602';
        $params['funCode'] = 'QueryOrderTrans';
        system_log("查询默联接口 F00010602 是否支付，请求参数".json_encode($params));
        $res = $this->callMlPost($params,1);
        system_log("查询默联接口 F00010602 是否支付，出参".json_encode($res));
        if($res['code'] != 1) return $res;
        if(empty($res['data'])){
            return ['code' => 0,'msg' => '默联订单查询接口 F00010602 未返回任何数据'];
        }
        // 判断默联是否有预期的返回值
        if($res['data']['RspCode'] != 0){
            return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
        }
        if(!isset($res['data']['OrderState']) || $res['data']['OrderState'] != '02'){
            return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
        }
        if(!isset($res['data']['TranState']) || $res['data']['TranState'] != '02'){
            return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
        }
//        dump($options);
        // 留个结果 // 0-挂号缴费,1-门诊缴费,2-门诊充值,3-住院充值
        $resCheck = $this->cheackAliMlPayOrder($res['data'],$options['openid'],$options['tran_no'],3);
        // true 通知his   false 已经通知
        if($resCheck){
            $payTime = $options['pay_time'];
            $Request_his['MethodName'] = 'ZyPayDeposit';
            $Request_his['PatId'] = $options['jz_card'];
            $Request_his['Inpatient_no'] = $options['inpatientno']; //
            $Request_his['Inhosp_Num'] = $options['admisstimes']; //
            $Request_his['OrderNo'] = $options['transaction_id'];
            $Request_his['Amount'] = $options['full_money'];
            $Request_his['Cheque_type'] = '11'; //与医院人员确认支付类型
            system_log('开始调用儿童医院his接口,住院支付订单:'.json_encode($Request_his));
            $res_his = $this->call($Request);
            system_log('his接口返回:'.json_encode($res_his));
            if(isset($res_his['Result'])&&$res_his['Result'] ==1){
                $data['id'] = $options['id'];
                $data['his_order_no'] = isset($res['hisOrdNum'])?$res['hisOrdNum']:0;//his支付订单号
                $data['status'] = 1;//支付成功
                $fullOrder = new FullOrder();
                $fullOrder->updateFullOrder($data);
                // 将支付信息再次通知默联
                try {

                    $RequestMl['OrderNo'] = $options['ml_order_no'];
                    $RequestMl['Hislsh'] = $options['transaction_id'];
                    $DataTableMl['data'] = $RequestMl;
                    $xml = new A2Xml();
                    $paramsMl['xml'] = $xml->toXml($DataTableMl);
                    // $data['funCode'] = 'F000120';
                    $paramsMl['funCode'] = 'UpdateOrderInfoForLSH';
                    system_log("儿童住院充值,通知默联请求:".json_encode($paramsMl));
                    $resMl = $this->callMlPost($paramsMl);
                    system_log("儿童住院充值,通知默联结果:".json_encode($resMl));
                } catch (\Exception $e) {
                    system_log("默联通知出现异常".$e->getMessage());
                }
                return self::returnSuccess([],'住院:全流程执行完成');
            } else {
                return self::returnErorr("住院:回写his支付数据失败");
            }
        }
        // 返回前段成功
        return self::returnSuccess([],"住院:已经通知his与默联");
    }

    // 默联门诊支付改造(支付宝小程序)   支付宝下单(F000225)
//    const ALIPAYOPID = "09933";
    function createZyMlAliPay($options = array()){
//        dump($options);die;
        $Request['MerBillNo'] = $options['order_no'];
        $Request['Amount'] = $options['full_money'];
        $Request['ChannelCode'] = 'ALIPAY_APPLETS';
        $Request['BuyerId'] = $options['openid'];
        $Request['AppName'] = '';
        $Request['OperUser'] = self::ALIPAYOPID; // 待确认
        $Request['SignType'] = '01';
        $signContent = strtoupper($Request['MerBillNo'].'|'.$Request['Amount'].
            '|'.$Request['ChannelCode'].'|'.$this->mlAliZySignKey);
        $Request['SignData'] = md5($signContent);
        $Request['NotifyUrl'] = '';
        $Request['ReturnUrl'] = '';
        $DataTable['data'] = $Request;
        $xml = new A2Xml();
        $paramsXml =  $xml->toXml($DataTable);
//        dump($paramsXml);die();
        $params['xml'] = $paramsXml;
//        $data['funCode'] = 'F000225';
        $params['funCode'] = 'UnifiedOrderForAliPay';
        system_log("alipay:通过默联进行支付宝下单,req:".json_encode($params));
        $res = $this->callMlPost($params,1);
        system_log("alipay:通过默联进行支付宝下单,res:".json_encode($res['data']));
        return $res;
    }
    // 默联住院充值改造(支付宝小程序)   推送缴费订单(F000102)
    function getZyChargePayIdMLAli($options = array()) {
//        dump($options);die;
        $order_id = $options['order_id'];
        $FullOrder = new FullOrder();
        $order_info = $FullOrder->getOrderInfo($order_id);
        if(!$order_info){
            $this->returnErorr("未找到待支付的订单");
        }

        /*if(!empty($order_info['ml_order_no'])){
            $res['data']['OrderNo'] = $order_info['ml_order_no'];
            echo 123;
        } else {*/
            $Request['MerBillNo'] = $order_info['order_no'];
            $Request['MerNo'] = $this->mlAliZyMerNo; // 由于住院充值和院区无关，默认使用前兴
            $Request['OrderDate'] = date("Y-m-d");
            $Request['OrderAmount'] = $order_info['full_money'];
            $Request['OrderSource'] = '05';
            $Request['OrderSourceCode'] = '04';
            $Request['ProductName'] = '充值';
            $Request['PatientName'] = $order_info['patient_name'];
            $Request['CreateBy'] = $this->createBy;
            $Request['OrderType'] = '06';
            $Request['SourceId'] = $order_info['jz_card'].'|'.$order_info['inpatientno'].'|'.$order_info['admisstimes'];
            $Request['PerAccount'] = $order_info['inpatientno'].'|'.$order_info['admisstimes']; // 个人账户号 = 住院号|住院次数
            $Request['SignType'] = '01';
            $signContent = strtoupper($Request['MerBillNo'].'|'.$Request['MerNo'].
                '|'.$Request['OrderDate'].'|'.$Request['OrderAmount'].'|'.
                '|'.$Request['OrderSource'].'|'.$Request['OrderSourceCode'].'|'.$this->mlAliZySignKey); // 由于住院充值和院区无关，默认使用前兴
            $Request['SignData'] = md5($signContent);
            $DataTable['data'] = $Request;
            $xml = new A2Xml();
            $params['xml'] = $xml->toXml($DataTable);
            // $data['funCode'] = 'F000103';
            $params['funCode'] = 'PushOrderInfo_Addmoney';
//            dump($params);die;
            $res = $this->callMlPost($params,1);
//            dump($res);die;
            // 判断前置机返回前端 0 表示失败
            if($res['code'] != 1) return $res;

            // 判断默联是否有返回值
            if(empty($res['data'])){
                return ['code' => 0,'msg' => 'his接口未返回任何数据'];
            }
//            dump($options);
            // 判断默联是否有预期的返回值
            if($res['data']['RspCode'] != 0){
                return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
            }

            // 记录 默联 订单号
            $resDb = Db::name('full_order')->where(['order_no'=>$order_info['order_no']])->update(['ml_order_no'=>$res['data']['OrderNo']]);
            $order_info['ml_order_no'] = $res['data']['OrderNo'];
            if(!$resDb){
                system_log('单号:'.$order_info['order_no'].'的订单更新默联单号:'.$res['data']['OrderNo'].'失败');
            }
//        }

        $tmp = Db::name('full_order_ext')->where(['id'=>$order_id])->find();
        if(!$tmp){
            $res1 = $this->createZyMlAliPay($order_info);
            if($res1['code'] != 1) return $res1;
            if(empty($res1['data'])){
                return ['code' => 0,'msg' => '默联支付宝下单接口 F000225 未返回任何数据'];
            }
            // 判断默联是否有预期的返回值
            if($res1['data']['RspCode'] != 0){
                return ['code'=> 0, 'msg'=>isset($res1['data']['RspCode']) ? $res1['data']['RspMsg'] : '未知的错误'];
            }

            // 默联创建成功保存下
            $tdata['id'] = $order_id;
            $tdata['tran_no'] = $res1['data']['TranNo'];
            $tdata['trade_no'] = $res1['data']['TradeNO'];
            $tdata['hos_id'] = $order_info['hos_id'];
            $tdata['openid'] = $order_info['openid'];
            Db::name('mz_pay_order_ext')->insert($tdata);
            $tmp['tran_no'] = $tdata['tran_no'];
            $tmp['trade_no'] = $tdata['trade_no'];
        }
//        dump($res);die;
        // 返回前端数据
        $res['data']['MerNo'] = $this->mlZyMerNo;
        $res['data']['OrderDate'] = date('Y-m-d');
        $res['data']['OrderAmount'] = $order_info['full_money'];
        $res['data']['openid'] = $order_info['openid'];
        $res['data']['order_id'] = $order_id;
        $res['data']['TranNo'] = $tmp['tran_no'];
        $res['data']['TradeNO'] = $tmp['trade_no'];
        return ['code'=>1,'msg'=>'获取成功','data'=>$res['data']];
    }

    // 默联住院充值改造
    function getZyChargePayIdML($options = array()) {
        $order_id = $options['order_id'];
        $FullOrder = new FullOrder();
        $order_info = $FullOrder->getOrderInfo($order_id);
        if(!$order_info){
            $this->returnErorr("未找到待支付的订单");
        }

        if(!empty($order_info['ml_order_no'])){
            $res['data']['OrderNo'] = $order_info['ml_order_no'];
        } else {
            $Request['MerBillNo'] = $order_info['order_no'];
            $Request['MerNo'] = $this->mlZyMerNo; // 由于住院充值和院区无关，默认使用前兴
            $Request['OrderDate'] = date("Y-m-d");
            $Request['OrderAmount'] = $order_info['full_money'];
//            $Request['OrderAmount'] = 5;
            $Request['OrderSource'] = '04';
            $Request['OrderSourceCode'] = '04';
            $Request['ProductName'] = '充值';
            $Request['PatientName'] = $order_info['patient_name'];
            $Request['CreateBy'] = $this->createBy;
            $Request['OrderType'] = '06';
            $Request['SourceId'] = $order_info['jz_card'].'|'.$order_info['inpatientno'].'|'.$order_info['admisstimes'];
            $Request['PerAccount'] = $order_info['inpatientno'].'|'.$order_info['admisstimes']; // 个人账户号 = 住院号|住院次数
            $Request['SignType'] = '01';
            $signContent = strtoupper($Request['MerBillNo'].'|'.$Request['MerNo'].
                '|'.$Request['OrderDate'].'|'.$Request['OrderAmount'].'|'.
                '|'.$Request['OrderSource'].'|'.$Request['OrderSourceCode'].'|'.$this->mlZySignKey); // 由于住院充值和院区无关，默认使用前兴
            $Request['SignData'] = md5($signContent);
            $DataTable['data'] = $Request;
            $xml = new A2Xml();
            $params['xml'] = $xml->toXml($DataTable);
            // $data['funCode'] = 'F000103';
            $params['funCode'] = 'PushOrderInfo_Addmoney';
//            dump($params);die;
            $res = $this->callMlPost($params);

//            dump($res);die;

            // 判断前置机返回前端 0 表示失败
            if($res['code'] != 1) return $res;

            // 判断默联是否有返回值
            if(empty($res['data'])){
                return ['code' => 0,'msg' => 'his接口未返回任何数据'];
            }
//            dump($options);
            // 判断默联是否有预期的返回值
            if($res['data']['RspCode'] != 0){
                return ['code'=> 0, 'msg'=>isset($res['data']['RspCode']) ? $res['data']['RspMsg'] : '未知的错误'];
            }

            // 记录 默联 订单号
            $resDb = Db::name('full_order')->where(['order_no'=>$order_info['order_no']])->update(['ml_order_no'=>$res['data']['OrderNo']]);
            if(!$resDb){
                system_log('单号:'.$order_info['order_no'].'的订单更新默联单号:'.$res['data']['OrderNo'].'失败');
            }
        }


        $res['data']['MerNo'] = $this->mlZyMerNo;
        $res['data']['OrderDate'] = date('Y-m-d');
        $res['data']['OrderAmount'] = $order_info['full_money'];
//        $res['data']['OrderAmount'] = 5;
        $res['data']['SignMD5'] = md5($order_info['order_no'].'|'.date('Y-m-d').'|'.$order_info['full_money'].'|'.$order_info['jz_card'].'|'.$this->mlZySignKey);
        $notifyUrl = url('api/zy/chargenotifyml',[],false,true);//默认的通知地址
        $res['data']['Merchanturl'] = '';
        $res['data']['S2SMerchanturl'] = $notifyUrl;
        $res['data']['openid'] = $order_info['openid'];
        $res['data']['order_id'] = $order_info['id'];
        $res['data']['ValidTime'] = date('Y-m-d H:i:s', strtotime("+20 minutes"));
        $res['data']['order_no'] = $order_info['order_no'];

//        dump($res);die;


        return ['code'=>1,'msg'=>'获取成功','data'=>$res['data']];
    }

    public function callMi($Request) {
        $Request['Organization'] = $this->Organization;
        $DataTable['MethodName'] = "HqWxPayService"; // 添加授权码
        $DataTable['AccessKey'] = $this->MiAccessKey;
        $DataTable['DataTable'] = $Request;
        $DocumentElement['DocumentElement'] = $DataTable;
        $xml = new A2Xml();
        $RequestXmlStr = $xml->toXml($DocumentElement);
        $RequestXmlStr = str_replace('UTF-8', 'gb2312', $RequestXmlStr);
        $post_data['xml'] = $RequestXmlStr;
        $post_data['soap_url'] = $this->mi_test_soap_url;
//        debug('hisstart');
        $post_data['timeout'] = 30;
        $res = http_post_request($this->mi_url, $post_data, 30);
        $result = json_decode($res, true);
        system_log($RequestXmlStr);
        system_log('调用儿童医院医保接口返回结果：'.json_encode($result));
//        debug('hisend');
//        $apiruntime = debug('hisstart','hisend',6);
//        if (intval($apiruntime) > 5) {
//            if (isset($result['runtime'])) {
//                system_log('调用儿童医院his接口:MethodName=' . $Request['MethodName'] . ' 时间：' . $apiruntime.'s  HIS执行时间：' . $result['runtime'].'s  参数：'.json_encode($Request). ' 返回结果：'.json_encode($result));
//            } else {
//                system_log('调用儿童医院his接口:MethodName=' . $Request['MethodName'] . ' 调用时间：' . $apiruntime.'s  参数：'.json_encode($Request). ' 返回结果：'.json_encode($result));
//            }
//        }
        //dump($result);die;
        if ($result['code'] == 1) { // 接口调用成功，不代表获取到数据
            if ($result['data']['Result'] != "0") {
                system_log('调用儿童医院his接口出错未获取到数据,返回结果：'.json_encode($result));
                $response = ['Result' => 0,'Error' => '未获取到数据'];
            } else {
                if ($result['data']['DataTable']) {
                    $response = $result['data']['DataTable'];
                } else {
                    $response = $result['data'];
                }
            }
            return $response;
        } else {
            system_log('调用儿童医院his接口出错:code=' . $result['code'] . ' msg=' . $result['msg'].' 参数：'.json_encode($Request) . ' 返回结果：'.json_encode($result));
            return ['Result' => 0,'Error' => $result['msg']];
        }
        return null;
    }

    public function call($Request) {
        $Request['Organization'] = $this->Organization; // 添加机构编码
//         $Request['Opera'] = $this->opera; // 操作员ID
        if ($Request['MethodName'] == "Mz_HuaJia") {
            $DataTable['MethodName'] = "HqWxPayService"; // 添加授权码
            $DataTable['AccessKey'] = $this->MiAccessKey; // 添加授权码
        } else {
            $Request['AccessKey'] = $this->AccessKey; // 添加授权码
        }
        $DataTable['DataTable'] = $Request;
//        $DataTable['MethodName'] = $Request['MethodName'];
        $DocumentElement['DocumentElement'] = $DataTable;

        $xml = new A2Xml();
        $RequestXmlStr = $xml->toXml($DocumentElement);
        $RequestXmlStr = str_replace('UTF-8', 'gb2312', $RequestXmlStr);
        $post_data['xml'] = $RequestXmlStr;
        if ($Request['MethodName'] == "Mz_HuaJia") {
            $post_data['soap_url'] = $this->mi_test_soap_url;
        }
        if (in_array($Request['MethodName'], self::$MZ_Method)) { // 门诊接口方法
            $post_data['soap_url'] = $this->mz_soap_url;
        } elseif (in_array($Request['MethodName'], self::$YY_Method)) { // 预约接口方法
            $post_data['soap_url'] = $this->bk_soap_url;
        } elseif (in_array($Request['MethodName'], self::$ZF_Method)) { // 支付接口方法
            $post_data['soap_url'] = $this->mzzf_soap_url;
        } elseif (in_array($Request['MethodName'], self::$ZY_Method)) { // 支付接口方法
            $post_data['soap_url'] = $this->zy_soap_url;
        } elseif (in_array($Request['MethodName'], self::$LIS_Method)) { // 支付接口方法
            $post_data['soap_url'] = $this->lis_soap_url;
        }
        debug('hisstart');
        // $result = send_post($this->hos_url, $post_data);
//        dump($post_data);
        if ($Request['MethodName'] == 'MZ_Payment' || $Request['MethodName'] == 'MZ_PaymentDYT'||$Request['MethodName'] =='ZyPayDeposit'||$Request['MethodName'] =='MZ_Payment2024') {
            $post_data['timeout'] = 29;
            $result = http_post_request($this->hos_url, $post_data, 30);
        } else {
            $post_data['timeout'] = 8;
            $result = http_post_request($this->hos_url, $post_data, 10);

        }
        $result = json_decode($result, true);
//        dump($result);die();
        system_log($RequestXmlStr);
        debug('hisend');
        $apiruntime = debug('hisstart','hisend',6);
        if (intval($apiruntime) > 5) {
            if (isset($result['runtime'])) {
                system_log('调用儿童医院his接口:MethodName=' . $Request['MethodName'] . ' 时间：' . $apiruntime.'s  HIS执行时间：' . $result['runtime'].'s  参数：'.json_encode($Request). ' 返回结果：'.json_encode($result));
            } else {
                system_log('调用儿童医院his接口:MethodName=' . $Request['MethodName'] . ' 调用时间：' . $apiruntime.'s  参数：'.json_encode($Request). ' 返回结果：'.json_encode($result));
            }
        }
        //dump($result);die;
        if (in_array($Request['MethodName'], self::$ZF_Method)) { // 支付接口方法打印此日志
            $data = isset($result['data']) ? $result['data'] : '';
        }
        if ($result['code'] == 1) { // 接口调用成功，不代表获取到数据
            $strxml = str_replace('gb2312', 'utf-8', $result['data']);
            $strxml = str_replace('&#x0', '', $strxml);
            $resultXml = simplexml_load_string($strxml);
            $resultJson = json_decode(json_encode($resultXml), TRUE); // 将xml转换成数组
            if (! empty($resultJson['DataTable'])) {
                $response = $resultJson['DataTable'];
            } else {
                system_log('调用儿童医院his接口出错未获取到数据:返回结果：'.$resultXml . ' 返回结果：'.json_encode($result));
                $response = ['Result' => 0,'Error' => '未获取到数据'];
            }
            return $response;
        } else {
            system_log('调用儿童医院his接口出错:code=' . $result['code'] . ' msg=' . $result['msg'].' 参数：'.json_encode($Request) . ' 返回结果：'.json_encode($result));
            return ['Result' => 0,'Error' => $result['msg']];
        }
        return null;
    }
    public function callPacs($api_mothed,$Request) {
        debug('pacsstart');
        $post_data['soap_url'] = $this->pacs_soap_url;
        $post_data['api_mothed'] = $api_mothed;
        $post_data['api_params'] = $Request;
        $post_data['timeout'] = 10;
        $result = http_post_request($this->pacs_url, $post_data, 15);
        $result = json_decode($result, true);
        debug('pacsend');
        $apiruntime = debug('pacsstart','pacsend',6);
        if (intval($apiruntime) > 7) {
            if (isset($result['runtime'])) {
                system_log('调用儿童医院pacs接口:MethodName=' . $api_mothed . ' 时间：' . $apiruntime.'s  HIS执行时间：' . $result['runtime'].'s  参数：'.json_encode($Request));
            } else {
                system_log('调用儿童医院pacs接口:MethodName=' . $api_mothed . ' 调用时间：' . $apiruntime.'s  参数：'.json_encode($Request));
            }
        }
        if ($result['code'] == 1) { // 接口调用成功，不代表获取到数据
            return $result['data'];
        } else {
            if ($result['msg'] != '该病人无报告！') {
                system_log('调用儿童医院pacs接口出错:code=' . $result['code'] . ' msg=' . $result['msg'] .' 参数：'.json_encode($Request). ' 返回结果：'.json_encode($result));
            }
            return ['ErrorCode' => 1,'ErrorMsg' => $result['msg']];
        }
        return null;
    }
    public function callLis($api_mothed,$Request) {
        debug('lisstart');
        $post_data['soap_url'] = $this->lis_soap_url;
        $post_data['api_mothed'] = $api_mothed;
        $post_data['api_params'] = $Request;
        $post_data['timeout'] = 120;
        $result = http_post_request($this->lis_url, $post_data, 120);
        $result = json_decode($result, true);
        debug('lisend');
        $apiruntime = debug('lisstart','lisend',6);
        if (intval($apiruntime) > 7) {
            if (isset($result['runtime'])) {
                system_log('调用儿童医院Lis接口:MethodName=' . $api_mothed . ' 时间：' . $apiruntime.'s  HIS执行时间：' . $result['runtime'].'s  参数：'.json_encode($Request) . ' 返回结果：'.json_encode($result));
            } else {
                system_log('调用儿童医院Lis接口:MethodName=' . $api_mothed . ' 调用时间：' . $apiruntime.'s  参数：'.json_encode($Request) . ' 返回结果：'.json_encode($result));
            }
        }
        if ($result['code'] == 1) { // 接口调用成功，不代表获取到数据
            system_log('调用儿童医院Lis接口:MethodName=' . $api_mothed . '  参数：'.json_encode($Request) . ' 返回结果：'.json_encode($result));
            return $result['data'];
        } else {
            system_log('调用儿童医院lis接口出错:code=' . $result['code'] . ' msg=' . $result['msg'].' 参数：'.json_encode($Request) . ' 返回结果：'.json_encode($result));
            return [];
        }
        return null;
    }
    // 将儿童医院的时段转换成滇医通的时段
    function timeTypeToDyt($timeType) {
        // 1 上午；2 中午；3 下午；4 晚上 => 1上午,2下午,3中午,4晚上
        switch ($timeType) {
            case '1' :
                return 1;
            case '2' :
                return 2;
            case '3' :
                return 3;
            case '4' :
                return 4;
            default:return 0;
        }
    }
    function timeTypeToStr($timeType) {
        $data = ['1' => '上午','2' => '中午','3' => '下午','4' => '晚上', '7' => '全天',  '0' => '未知'];
        return $data[$timeType];
    }
    function getDeparts($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
        }
         CacheManager::rmDepartList($options['hos_code']);
        $departs = CacheManager::getDepartList($options['hos_code']);
        if ($departs) { // 缓存数据
            return $this->returnSuccess($departs);
        }

        // 获取数据库中的数据
        $departs = Db::name('Depart')->where(['hos_id'=>$options['hos_code'],'category_id'=>['gt',0]])->order('sort ASC')->select();
//        dump($departs);die;
        CacheManager::setDepartList($options['hos_code'], $departs);
        return $this->returnSuccess($departs);


        // * 动态调用医院的科室获取接口
        /*$hosCode = $options['hos_code'];
        $DeptNo = $this->hos_code_map[$hosCode];
        $Request['MethodName'] = 'BK_GetOrgDept';
        $Request['DeptNo'] = $DeptNo;
        $response = $this->call($Request);
        if (!isset($response['Result']) && $response) { // 获取科室成功
            $py = new Pinyin();
            $departs_ids = Db::name('Depart')->where('hos_id', $hosCode)->column('dep_id'); // 查询数据库存储的科室ID
            $noInDbDeaprts = array(); // 在数据库中不存在的科室
            foreach ( $response as $k => $value ) {
                $depName = $value['DeptName'];
                if(strstr($depName,'急诊')||in_array($value['DeptSn'],['2323','0231577','0231540','210103','0231523','0231676','230324','H21004','H21002'])) continue;//不预约的科室
//                 $search = array('(特需门诊)','（特需门诊）','(前兴)','（前兴）','(书林)','（书林）','(南)','（南）');
//                 $replace = array('','','','','','','','');
//                 $depName = str_replace($search, $replace, $depName);
                if($options['hos_code'] == '871003' && $value['DeptSn'] == '2304') continue;
                $char = strtoupper($py->getJp($depName));
                $departs[$k]['hos_id'] = $hosCode;
                $departs[$k]['dep_id'] = $value['DeptSn'];
                $departs[$k]['dep_name'] = $depName;
                $departs[$k]['first_char'] = substr($char, 0, 1);
                $departs[$k]['char'] = $char;
                $departs[$k]['dep_intro'] = ''; // $value['DeptDes'];
                                                
                // 记录数据库中不存在的科室
                if (! in_array($value['DeptSn'], $departs_ids)) {
                    array_push($noInDbDeaprts, $departs[$k]);
                }
            }
            // 将数据库中不存在的科室保存到数据库
            if (count($noInDbDeaprts)) {
                Db::name('Depart')->strict(false)->insertAll($noInDbDeaprts); // 关闭字段严格检查,将数据添加到数据库
            }
            $departs = array_sort($departs, 'char');
            $departs = array2array($departs);
            CacheManager::setDepartList($options['hos_code'], $departs);
            return $this->returnSuccess($departs);
        } else {
            return $this->returnErorr('未获取到科室信息');
        }*/
    }

    function getCovidItemList(){
        // 9199 新型冠状病毒核酸检测
        // 9213 新型冠状病毒核酸检测组合
        // 9215 新型冠状病毒核酸检测（快速法）
        // 9241 新型冠状病毒核酸检测（双采双检）
        $Request['MethodName'] = 'GetJYItem';
        $Request['ClassID'] = '';
        $Request['ItemId'] = '9213';
        // dump($Request);die;
        $response = $this->call($Request);
        dump($response);die;

    }

    function postCovidItemInfo($params){
        $Request['MethodName'] = 'InsertDetailHS';
        $Request['PatID1'] = $params['jz_card'];
        $Request['ApplyDept'] = $params['dep_id'];
        $Request['ApplyDoctor'] = $params['doc_id'];
        $Request['RecordDate'] = date('Y-m-d H:i:s');
        $Request['ExecDept'] = $params['dep_id'];
        $Request['Predata'] = $params['pre_data'];
        system_log("covid_create_req".json_encode($Request));
//        var_dump($Request);
        $response = $this->call($Request);
        system_log("covid_create_res".json_encode($response));
//        var_dump($response);
        if (isset($response['Result']) && $response['Result'] == 0){
            return $this->returnSuccess([],'开单成功');
        } else {
            return $this->returnErorr(isset($response['Error']) ? $response['Error'] : '开单失败');
        }
    }

    function changeCreditHis($params){
        $Request['MethodName'] = 'SethmdSys';
        $Request['PID'] = $params['jz_card'];
        $Request['HmdSys'] = $params['is_credit'] == 1 ? 'S':'';
        $response = $this->call($Request);
        if (isset($response['Result']) && $response['Result'] == 0){
            return $this->returnSuccess([],'操作成功');
        } else {
            return $this->returnErorr(isset($response['Error']) ? $response['Error'] : '同步his失败');
        }
    }



    public function getElectronicBill($transactionId, $patientMedicalCard, $billId) {
        $Request['MethodName'] = 'Mz_getNetUrl';
        $Request['PatientID'] = $patientMedicalCard;
        $Request['Times'] = $billId;
        $Request['Mobilesn'] = $transactionId;
        $response = $this->call($Request);
        if (isset($response['Result']) && $response['Result'] == 1 && !empty($response['URL'])){
            return $response['URL'];
        }
        return null;
    }

    function postAntigenItemInfo($params){
        $Request['MethodName'] = 'InsertDetailHS_9253';
        $Request['PatID1'] = $params['jz_card'];
        $Request['ApplyDept'] = $params['dep_id'];
        $Request['ApplyDoctor'] = $params['doc_id'];
        $Request['RecordDate'] = date('Y-m-d H:i:s');
        $Request['ExecDept'] = $params['dep_id'];
        $Request['Predata'] = $params['pre_data'];
        system_log("antigen_create_req".json_encode($Request));
//        var_dump($Request);
        $response = $this->call($Request);
        system_log("antigen_create_res".json_encode($response));
//        var_dump($response);
        if (isset($response['Result']) && $response['Result'] == 0){
            return $this->returnSuccess([],'开单成功');
        } else {
            return $this->returnErorr(isset($response['Error']) ? $response['Error'] : '开单失败');
        }
    }

    function getPatientNumsFromHis($params){
        //A000021 儿内科(前兴)H
        //A000126急诊前兴H区
        // A000051发热普通门诊（书林）
        // 3014 急诊门诊（书林）
        $Request['MethodName'] = 'GetPopulation';
        $Request['Dept'] = 'A000021|A000126|A000051|3014';
//        dump($Request);
        $response = $this->call($Request);
//        dump($response);die;
        $arr[0]= 0;
        $arr[1]= 0;
        foreach ($response as $v){
           $tmp =  explode(":",$v['Error']);
           if($tmp[0] == "前兴"){
               $arr[0] = $tmp[1];
           } else{
               $arr[1] = $tmp[1];
           }
        }
        /*$arr[0] = empty($response['Error']) ? 0 : $response['Error'];
        $Request['Dept'] = 'A000051|3014';
        $response = $this->call($Request);
        $arr[1] = empty($response['Error']) ? 0 : $response['Error'];*/
        return $arr;
    }

    function getDoctors($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
        }
        $hosId = $options['hos_code'];
        $DeptNo = $this->hos_code_map[$hosId];
        $Request['MethodName'] = 'BK_GetSchedule';
//         $Request['MethodName'] = 'BK_GetSchedule_Test';
        $Request['DeptSn'] = $options['dep_id'];
        $Request['DoctCode'] = ''; // 查询医生列表时
        $Request['DeptNo'] = $DeptNo;
        $Request['AccessKey'] = $this->AccessKey; // 添加授权码
        $Request['Organization'] = $this->Organization; // 添加机构编码
        $Request['BeginDate'] = isset($options['from_date'])?$options['from_date']:'';
        $Request['EndDate'] = isset($options['end_date'])?$options['end_date']:'';
        $DataTable['DataTable'] = $Request;
        $DocumentElement['DocumentElement'] = $DataTable;
        $xml = new A2Xml();
        $RequestXmlStr = $xml->toXml($DocumentElement);
        $RequestXmlStr = str_replace('UTF-8', 'gb2312', $RequestXmlStr);

        $post_data['xml'] = $RequestXmlStr;
        $post_data['soap_url'] = $this->bk_soap_url;
        $post_data['timeout'] = 15;
//        dump($this->doctors_url);
//        dump($post_data);
        $resultstr = http_post_request($this->doctors_url, $post_data, 15);
        $result = json_decode($resultstr, true);
//        dump($result);die();
        $doc_arr = array();
        $yearStr = date('Y') . '-';
        $nextYearStr = (intval(date('Y'))+1) . '-';
        if ($result['code'] == 1) {
            $search = array('(特需门诊)','（特需门诊）','(前兴)','（前兴）','(书林)','（书林）','(南)','（南）');
            $replace = array('','','','','','','','');
            foreach ( $result['data'] as $key => $value ) {
                if(empty($key))continue;
                $doc_arr[$key]['hos_id'] = $options['hos_code'];
                $doc_arr[$key]['dep_id'] = $value['DeptSn'];
                $value['Deptname'] = str_replace($search, $replace, $value['Deptname']);
                $doc_arr[$key]['dep_name'] = $value['Deptname'];
                $doc_arr[$key]['doc_id'] = $value['DoctCode'];
                $doc_arr[$key]['doc_name'] = $value['DoctorName'];
                if (isset($value['jz_time'])) {
                    $sch_date_list = explode(' ', $value['jz_time']);
                    sort($sch_date_list);
                    $sch_date_list = implode(' ', $sch_date_list);
                    $sch_date = str_replace([$yearStr, $nextYearStr], ['',''], $sch_date_list);
                    $doc_arr[$key]['sch_date'] = $sch_date; // 返回的去除年份的显示
                }
//                 $doc_arr[$key]['sch_date'] = str_replace($yearStr, '', $value['jz_time']); // 返回的去除年份的显示
//                 $doc_arr[$key]['sch_date'] = str_replace($nextYearStr, '', $doc_arr[$key]['sch_date']); // 返回的去除年份的显示
                $doc_arr[$key]['sch_date_full'] = $value['jz_time'];
                $doc_arr[$key]['amt'] = 0; //
                $doc_arr[$key]['level_name'] = $value['ClinicResponceName'];

                $doc_arr[$key]['src_num'] = $value['CanBookNum'];
                $doc_arr[$key]['book_status'] = isset($value['BookStatus'])?$value['BookStatus']:0;
                $doc_arr[$key]['SchDate'] = isset($value['SchDate'])?$value['SchDate']:[];
                $doc_arr[$key]['book_enable_status'] = isset($value['BookFlag'])?$value['BookFlag']:0;
//                $doc_arr[$key]['book_enable'] = $value['BookEnable'];
                //$doc_detail = $this->getDoctorDetail($hosId, $options['dep_id'], $value['DoctCode']);
                $doc_detail = $this->getDoctorDetail($hosId, $value['DeptSn'], $value['DoctCode']);
                if ($doc_detail === false) {
                    // 缓存中不存在
                    // 查询表是否有此列值
                    // 组装数据录入数据表doctor_info
                    $insert = ['hos_id' => $hosId,'dep_id' => $value['DeptSn'],'doc_id' => $value['DoctCode'],'doc_avatar' => '','doc_name' => $value['DoctorName'],'level_name' => $value['ClinicResponceName'],'title_name' => $value['ClinicResponceName'],'doc_good' => '','doc_info' => '','status' => 1];
                    model('Doctor')->save($insert);

                    $doc_arr[$key]['doc_avatar'] = '';
                    $doc_arr[$key]['doc_good'] = '';
                } else {

                    if ($doc_arr[$key]['level_name'] && $doc_arr[$key]['level_name'] != $doc_detail['level_name']) {
                        try {
                            $doc_detail['level_name'] = $doc_arr[$key]['level_name'];
                            db('Doctor')->where(['id'=>$doc_detail['id']])->update(['level_name'=>$doc_arr[$key]['level_name']]);
                        } catch (\Exception $e) {
                            system_log('更新医生身份出错:'.$e->getMessage());
                        }
                    } else {
                        $doc_arr[$key]['level_name'] = $doc_detail['level_name'];
                    }
                    $doc_arr[$key]['doc_avatar'] = $doc_detail['doc_avatar'];
                    $doc_arr[$key]['doc_good'] = $doc_detail['doc_good'];
                    $doc_arr[$key]['doc_info'] = $doc_detail['doc_info'];
                    $doc_arr[$key]['title_name'] = isset($doc_detail['title_name'])?$doc_detail['title_name']:'';

                }
//                if($value['BookFlag']==4){
//                    //0,可约，1,约满，2，停诊
//                    $doc_arr[$key]['book_enable_status'] = 2;
//                }else if($value['CanBookNum']>0){
//                    $doc_arr[$key]['book_enable_status'] = 0;
//                }else{
//                    $doc_arr[$key]['book_enable_status'] = 1;
//                }
            }
            $doc_arr = array2array($doc_arr);
            if(empty($doc_arr))return $this->returnErorr('没有可以预约的医生');
            return $this->returnSuccess($doc_arr);
        } else {
            try {
                system_log('调用医生列表接口出错：结果'.$resultstr.' 参数：'.json_encode($Request));
            } catch (\Exception $e) {
            }
            return $this->returnErorr('没有可以预约的医生');
        }
    }
    function getDoctorsnew($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
        }
        $hosId = $options['hos_code'];
        $DeptNo = $this->hos_code_map[$hosId];
        $Request['MethodName'] = 'BK_GetSchedule_Stop';
//         $Request['MethodName'] = 'BK_GetSchedule_Test';
        $Request['DeptSn'] = $options['dep_id'];
        $Request['DoctCode'] = ''; // 查询医生列表时
        $Request['DeptNo'] = $DeptNo;
        $Request['AccessKey'] = $this->AccessKey; // 添加授权码
        $Request['Organization'] = $this->Organization; // 添加机构编码
        $Request['BeginDate'] = isset($options['from_date'])?$options['from_date']:'';
        $Request['EndDate'] = isset($options['end_date'])?$options['end_date']:'';
        $DataTable['DataTable'] = $Request;
        $DocumentElement['DocumentElement'] = $DataTable;
        $xml = new A2Xml();
        $RequestXmlStr = $xml->toXml($DocumentElement);
        $RequestXmlStr = str_replace('UTF-8', 'gb2312', $RequestXmlStr);

        $post_data['xml'] = $RequestXmlStr;
        $post_data['soap_url'] = $this->bk_soap_url;
        $result = http_post_request($this->doctors_url_new, $post_data);
        $result = json_decode($result, true);
//        dump($result);die();
        $doc_arr = array();
        $yearStr = date('Y') . '-';
        $nextYearStr = (intval(date('Y'))+1) . '-';
        if ($result['code'] == 1) {
            $search = array('(特需门诊)','（特需门诊）','(前兴)','（前兴）','(书林)','（书林）','(南)','（南）');
            $replace = array('','','','','','','','');
            foreach ( $result['data'] as $key => $value ) {
                $doc_arr[$key]['hos_id'] = $options['hos_code'];
                $doc_arr[$key]['dep_id'] = $value['DeptSn'];
                $value['Deptname'] = str_replace($search, $replace, $value['Deptname']);
                $doc_arr[$key]['dep_name'] = $value['Deptname'];
                $doc_arr[$key]['doc_id'] = $value['DoctCode'];
                $doc_arr[$key]['doc_name'] = $value['DoctorName'];

                if (isset($value['jz_time'])) {
                    $sch_date_list = explode(' ', $value['jz_time']);
                    sort($sch_date_list);
                    $sch_date_list = implode(' ', $sch_date_list);
                    $sch_date = str_replace([$yearStr, $nextYearStr], ['',''], $sch_date_list);
                    $doc_arr[$key]['sch_date'] = $sch_date; // 返回的去除年份的显示
                }
//                 $sch_date = str_replace([$yearStr, $nextYearStr], ['',''], $value['jz_time']);
//                 $doc_arr[$key]['sch_date'] = str_replace($yearStr, '', $value['jz_time']); // 返回的去除年份的显示
//                 $doc_arr[$key]['sch_date'] = str_replace($nextYearStr, '', $doc_arr[$key]['sch_date']); // 返回的去除年份的显示
                $doc_arr[$key]['sch_date_full'] = $value['jz_time'];
                $doc_arr[$key]['amt'] = 0; //
                $doc_arr[$key]['level_name'] = $value['ClinicResponceName'];
                $doc_arr[$key]['src_num'] = $value['CanBookNum'];
                $doc_arr[$key]['SchDate'] = isset($value['SchDate'])?$value['SchDate']:[];
                //$doc_detail = $this->getDoctorDetail($hosId, $options['dep_id'], $value['DoctCode']);
                $doc_detail = $this->getDoctorDetail($hosId, $value['DeptSn'], $value['DoctCode']);
                if ($doc_detail === false) {
                    // 缓存中不存在
                    // 查询表是否有此列值
                    // 组装数据录入数据表doctor_info
                    $insert = ['hos_id' => $hosId,'dep_id' => $value['DeptSn'],'doc_id' => $value['DoctCode'],'doc_avatar' => '','doc_name' => $value['DoctorName'],'level_name' => $value['ClinicResponceName'],'title_name' => $value['ClinicResponceName'],'doc_good' => '','doc_info' => '','status' => 1];
                    model('Doctor')->save($insert);

                    $doc_arr[$key]['doc_avatar'] = '';
                    $doc_arr[$key]['doc_good'] = '';
                } else {

                    if ($doc_arr[$key]['level_name'] && $doc_arr[$key]['level_name'] != $doc_detail['level_name']) {
                        try {
                            $doc_detail['level_name'] = $doc_arr[$key]['level_name'];
                            db('Doctor')->where(['id'=>$doc_detail['id']])->update(['level_name'=>$doc_arr[$key]['level_name']]);
                        } catch (\Exception $e) {
                            system_log('更新医生身份出错:'.$e->getMessage());
                        }
                    } else {
                        $doc_arr[$key]['level_name'] = $doc_detail['level_name'];
                    }
                    $doc_arr[$key]['doc_avatar'] = $doc_detail['doc_avatar'];
                    $doc_arr[$key]['doc_good'] = $doc_detail['doc_good'];
                    $doc_arr[$key]['doc_info'] = $doc_detail['doc_info'];
                    $doc_arr[$key]['title_name'] = isset($doc_detail['title_name'])?$doc_detail['title_name']:'';

                }
            }
            $doc_arr = array2array($doc_arr);
            return $this->returnSuccess($doc_arr);
        } else {
            return $this->returnErorr('没有可以预约的医生');
        }
    }
    function getProDoctors($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
        }
        $hosId = $options['hos_code'];
        $DeptNo = $this->hos_code_map[$hosId];
        $Request['MethodName'] = 'BK_GetProSchedule';
//         $Request['MethodName'] = 'BK_GetProSchedule_Test';
        $Request['DeptSn'] = (isset($options['dep_id']) && $options['dep_id'])?$options['dep_id']:'';
        $Request['DoctCode'] = ''; // 查询医生列表时
        $Request['DeptNo'] = $DeptNo;
        $Request['AccessKey'] = $this->AccessKey; // 添加授权码
        $Request['Organization'] = $this->Organization; // 添加机构编码
        $Request['BeginDate'] = isset($options['from_date'])?$options['from_date']:'';
        $Request['EndDate'] = isset($options['end_date'])?$options['end_date']:'';
        $DataTable['DataTable'] = $Request;
        $DocumentElement['DocumentElement'] = $DataTable;
        $xml = new A2Xml();
        $RequestXmlStr = $xml->toXml($DocumentElement);
        $RequestXmlStr = str_replace('UTF-8', 'gb2312', $RequestXmlStr);

        $post_data['xml'] = $RequestXmlStr;
        $post_data['soap_url'] = $this->bk_soap_url;
        $resultstr = http_post_request($this->doctors_url, $post_data);
        $result = json_decode($resultstr, true);
        $doc_arr = array();
        $yearStr = date('Y') . '-';
        $nextYearStr = (intval(date('Y'))+1) . '-';
        $py = new Pinyin();
        if ($result['code'] == 1) {

            $search = array('(特需门诊)','（特需门诊）','(前兴)','（前兴）','(书林)','（书林）','(南)','（南）');
            $replace = array('','','','','','','','');
            foreach ( $result['data'] as $key => $value ) {
                $doc_arr[$key]['hos_id'] = $options['hos_code'];
                $doc_arr[$key]['dep_id'] = $value['DeptSn'];
                $value['Deptname'] = str_replace($search, $replace, $value['Deptname']);
                $doc_arr[$key]['dep_name'] = $value['Deptname'];
                $doc_arr[$key]['doc_id'] = $value['DoctCode'];
                $doc_arr[$key]['doc_name'] = $value['DoctorName'];
                $doc_arr[$key]['dep_char'] = strtoupper($py->getJp($value['Deptname']));
                if (isset($value['jz_time'])) {
                    $sch_date_list = explode(' ', $value['jz_time']);
                    sort($sch_date_list);
                    $sch_date_list = implode(' ', $sch_date_list);
                    $sch_date = str_replace([$yearStr, $nextYearStr], ['',''], $sch_date_list);
                    $doc_arr[$key]['sch_date'] = $sch_date; // 返回的去除年份的显示
                }
//                 $sch_date = str_replace([$yearStr, $nextYearStr], ['',''], $value['jz_time']);
//                 $sch_date = str_replace($nextYearStr, '', $sch_date);
//                 $doc_arr[$key]['sch_date'] = $sch_date; // 返回的去除年份的显示
//                 $doc_arr[$key]['sch_date'] = str_replace($nextYearStr, '', $doc_arr[$key]['sch_date']); // 返回的去除年份的显示
                $doc_arr[$key]['sch_date_full'] = $value['jz_time'];
                $doc_arr[$key]['amt'] = 0; //
                $doc_arr[$key]['level_name'] = isset($value['ClinicResponceName'])?$value['ClinicResponceName']:'';
                $doc_arr[$key]['src_num'] = $value['CanBookNum'];
                $doc_arr[$key]['book_status'] = isset($value['BookStatus'])?$value['BookStatus']:0;
                $doc_arr[$key]['SchDate'] = isset($value['SchDate'])?$value['SchDate']:[];

                //$doc_detail = $this->getDoctorDetail($hosId, $options['dep_id'], $value['DoctCode']);
                $doc_detail = $this->getDoctorDetail($hosId, $value['DeptSn'], $value['DoctCode']);
                if ($doc_detail === false) {
                    // 缓存中不存在
                    // 查询表是否有此列值
                    // 组装数据录入数据表doctor_info
                    $insert = ['hos_id' => $hosId,'dep_id' => $value['DeptSn'],'doc_id' => $value['DoctCode'],'doc_avatar' => '','doc_name' => $value['DoctorName'],'level_name' => $value['ClinicResponceName'],'title_name' => $value['ClinicResponceName'],'doc_good' => '','doc_info' => '','status' => 1];
                    model('Doctor')->insert($insert);

                    $doc_arr[$key]['doc_avatar'] = '';
                    $doc_arr[$key]['doc_good'] = '';
                } else {
                    if ($doc_arr[$key]['level_name'] && $doc_arr[$key]['level_name'] != $doc_detail['level_name']) {
                        try {
                            $doc_detail['level_name'] = $doc_arr[$key]['level_name'];
                            db('Doctor')->where(['id'=>$doc_detail['id']])->update(['level_name'=>$doc_arr[$key]['level_name']]);
                        } catch (\Exception $e) {
                            system_log('更新医生身份出错:'.$e->getMessage());
                        }
                    } else {
                        $doc_arr[$key]['level_name'] = $doc_detail['level_name'];
                    }
                    $doc_arr[$key]['doc_avatar'] = $doc_detail['doc_avatar'];
                    $doc_arr[$key]['doc_good'] = $doc_detail['doc_good'];
                    $doc_arr[$key]['doc_info'] = $doc_detail['doc_info'];
                    $doc_arr[$key]['title_name'] = isset($doc_detail['title_name'])?$doc_detail['title_name']:'';
                }
            }
            $doc_arr = array2array($doc_arr);
//            dump($doc_arr);die;
            $dbProDocList = $this->getDbProDoctors($hosId);
            $docId_arr = array_column($doc_arr,'doc_id');
            foreach($dbProDocList as $doc){
                if(!in_array($doc['doc_id'],$docId_arr)){
                    $doc['dep_name'] = str_replace($search, $replace, $doc['dep_name']);
                    $doc['dep_char'] = strtoupper($py->getJp($doc['dep_name'] ));
                    $doc['sch_date'] = '';
                    $doc['sch_date_full'] = '';
                    $doc['amt'] = 0;
                    $doc['src_num'] = 0;
                    $doc['SchDate'] = [];
                    array_push($doc_arr,$doc);
                }
            }
            $doc_arr = array_sort($doc_arr,'dep_char');
            $doc_arr = array2array($doc_arr);
            return $this->returnSuccess($doc_arr);
        } else {
            try {
                system_log('调用专家列表接口出错：结果'.$resultstr.' 参数：'.json_encode($Request));
            } catch (\Exception $e) {
            }
            return $this->returnErorr('没有可以预约的医生');
        }
    }
    function getDoctorForOrder($options = array()) {
        $hosId = $options['hos_id'];
        $doc_info = $this->getDoctorDetail($options['hos_id'], $options['dep_id'], $options['doc_id']);
        if ($doc_info) {
            $data['doc_id'] = $doc_info['doc_id'];
            $data['doc_name'] = $doc_info['doc_name'];
            $data['level_name'] = $doc_info['level_name'];
            $data['title_name'] = isset($doc_info['title_name'])?$doc_info['title_name']:'';
            $data['doc_good'] = $doc_info['doc_good'];
            $data['sch_date'] = $options['sch_date'];
            $data['sch_time_type'] = $options['time_type'];
            $data['amt'] = $hosId == '871006' ? $this->tx_payamt : 0; // 特需门诊支付100
            $data['dep_id'] = $options['dep_id'];
            $data['dep_name'] = $doc_info['dep_name'];
            $data['hos_code'] = $options['hos_code'];
            $data['schedule_id'] = $options['schedule_id'];
            return $this->returnSuccess($data);
        } else {
            return $this->returnErorr('未获取到医生信息');
        }
    }
    function getDocSchedule($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
        }
        $hosId = $options['hos_code'];
        $Request['MethodName'] = 'BK_GetSchedule';
//         $Request['MethodName'] = 'BK_GetSchedule_Test';
        $Request['DeptNo'] = $this->hos_code_map[$hosId];
        $Request['DeptSn'] = $options['dep_id'];
        $Request['DoctCode'] = $options['doc_id'];
        $Request['BeginDate'] = isset($options['from_date'])?$options['from_date']:'';
        $Request['EndDate'] = isset($options['end_date'])?$options['end_date']:'';
        // dump($Request);die;
        $response = $this->call($Request);
//        system_log('调用专家排班接口：'.json_encode($response));
        if (isset($response['Result']) && $response['Result'] == 0) {
            return $this->returnErorr(isset($response['msg']) ? $response['msg'] : '未获取到医生排班');
        }

        try {
            cache('api_doc_schedule' . '_' . $hosId . '_' . $options['dep_id'] . '_' . $options['doc_id'].'_'.$options['from_date'].'_'.$options['end_date'], $response, 5); // 将医生排班缓存5秒
        } catch (\Exception $e) {
        }
        $sch_arr = array();
        if (isset($response[0])) {
            $resArr = $response;
        } else {
            $resArr[0] = $response;
        }
        $resArr = array_sort($resArr, 'BeginDate');
        $resultArray=[];
        foreach ( $resArr as $k => $value ) {
//             $sch_arr[$value['Schedule']]['schedule_id'] = $value['Schedule'];
//             $sch_arr[$value['Schedule']]['sch_date'] = substr($value['BeginDate'], 0, 10);
//             $sch_arr[$value['Schedule']]['time_type'] = $this->timeTypeToDyt($value['Shift']);
//             $sch_arr[$value['Schedule']]['time_type_desc'] = $this->timeTypeToStr($value['Shift']);
//             $src_num = isset($sch_arr[$value['Schedule']]['src_num']) ? $sch_arr[$value['Schedule']]['src_num'] : 0;
//             $sch_arr[$value['Schedule']]['src_num'] = $src_num + $value['CanBookNum'];
//             if ($hosId == '871006') {
//                 $sch_arr[$value['Schedule']]['amt'] = $this->tx_payamt; // 特需门诊支付金额
//             }

            $arrkey = 'sch'.$value['Schedule'];
            $sch_arr[$arrkey]['schedule_id'] = $value['Schedule'];
            $sch_arr[$arrkey]['sch_date'] = substr($value['BeginDate'], 0, 10);
            $sch_arr[$arrkey]['time_type'] = $this->timeTypeToDyt($value['Shift']);
            $sch_arr[$arrkey]['time_type_desc'] = $this->timeTypeToStr($value['Shift']);
            if(!isset($value['BookingType'])){
                $value['BookingType']=0;
            }

            if($value['BookStatus']!=15&&$value['BookingType']==1&& $value['BookEnable']=="true"){
                $src_num = isset($sch_arr[$arrkey]['src_num']) ? $sch_arr[$arrkey]['src_num'] : 0;
                $sch_arr[$arrkey]['src_num'] = $src_num + $value['CanBookNum'];
                $resultArray[$k]=$value;
            }

//            }

             if ($hosId == '871006') {
                //$sch_arr[$arrkey]['amt'] = $this->tx_payamt; // 特需门诊支付金额
            }
        }
        //TODO
        //$sch_arr = array2array($sch_arr);
        return $this->returnSuccess($sch_arr);
    }
    function getProDocSchedule($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
        }
        $hosId = $options['hos_code'];
        $Request['MethodName'] = 'BK_GetProSchedule';
//         $Request['MethodName'] = 'BK_GetProSchedule_Test';
        $Request['DeptNo'] = $this->hos_code_map[$hosId];
        $Request['DeptSn'] = $options['dep_id'];
        $Request['DoctCode'] = $options['doc_id'];
        $Request['BeginDate'] = isset($options['from_date'])?$options['from_date']:'';
        $Request['EndDate'] = isset($options['end_date'])?$options['end_date']:'';
        // dump($Request);die;
        $response = $this->call($Request);
        // dump($response);die;
        if (isset($response['Result']) && $response['Result'] == 0) {
            return $this->returnErorr(isset($response['msg']) ? $response['msg'] : '未获取到医生排班');
        }
        cache('api_pro_doc_schedule' . '_' . $hosId . '_' . $options['dep_id'] . '_' . $options['doc_id'].'_'.$options['from_date'].'_'.$options['end_date'], $response, 5); // 将医生排班缓存5秒
        $sch_arr = array();
        if (isset($response[0])) {
            $resArr = $response;
        } else {
            $resArr[0] = $response;
        }
        $resArr = array_sort($resArr, 'BeginDate');
        foreach ( $resArr as $k => $value ) {
            $arrkey = 'sch'.$value['Schedule'];
            $sch_arr[$arrkey]['schedule_id'] = $value['Schedule'];
            $sch_arr[$arrkey]['sch_date'] = substr($value['BeginDate'], 0, 10);
            $sch_arr[$arrkey]['time_type'] = $this->timeTypeToDyt($value['Shift']);
            $sch_arr[$arrkey]['time_type_desc'] = $this->timeTypeToStr($value['Shift']);
            $src_num = isset($sch_arr[$arrkey]['src_num']) ? $sch_arr[$arrkey]['src_num'] : 0;
            $sch_arr[$arrkey]['src_num'] = $src_num + $value['CanBookNum'];
            $sch_arr[$arrkey]['book_status'] = isset($value['BookStatus'])?$value['BookStatus']:0;
            if ($hosId == '871006') {
                //$sch_arr[$arrkey]['amt'] = $this->tx_payamt; // 特需门诊支付金额
            }
        }
        //$sch_arr = array2array($sch_arr);
        return $this->returnSuccess($sch_arr);
    }
    function getDatePartSchedule($options = array()) {
        $schedule_id = $options['schedule_id'];
        $hosId = $options['hos_code'];
        $Request['MethodName'] = 'BK_GetSchedule';
//         $Request['MethodName'] = 'BK_GetSchedule_Test';
        $Request['DeptNo'] = $this->hos_code_map[$hosId];
        $Request['DeptSn'] = $options['dep_id'];
        $Request['DoctCode'] = $options['doc_id'];
        $Request['BeginDate'] = isset($options['from_date'])?$options['from_date']:'';
        $Request['EndDate'] = isset($options['end_date'])?$options['end_date']:'';

        $response = [];
        try {
            $response = cache('api_doc_schedule' . '_' . $hosId . '_' . $options['dep_id'] . '_' . $options['doc_id'].'_'.$options['from_date'].'_'.$options['end_date']);
        } catch (\Exception $e) {
        }
        if (empty($response)) { // 在获取医生排班的时候进行缓存5秒getDocSchedule
            $response = $this->call($Request);
            if (isset($response['Result']) && $response['Result'] == 0) { // 失败
                return $this->returnErorr('未获取到分时段信息');
            }
        }
        $time_schedule = array();
        if (isset($response[0])) {
            $resArr = $response;
        } else {
            $resArr[0] = $response;
        }
        $idx = 1;
        foreach ( $resArr as $k => $value ) {
            if ($schedule_id == $value['Schedule']) { // 提取某一排班id的数据
                $time_schedule[$k]['time_type'] = $value['Shift'];
                $time_schedule[$k]['sch_date'] = substr($value['BeginDate'], 0, 10);
                $time_schedule[$k]['queue_sn'] = $value['QueueSn'];
                $time_schedule[$k]['start_time'] = substr($value['BeginDate'], 11, 5);
                $time_schedule[$k]['end_time'] = substr($value['EndDate'], 11, 5);
                $time_schedule[$k]['src_num'] = $value['CanBookNum'];
                $time_schedule[$k]['book_status'] = isset($value['BookStatus'])?$value['BookStatus']:0;
                $time_schedule[$k]['book_enable']= $value['BookEnable'];
                $time_schedule[$k]['booking_type'] = isset($value['BookingType'])?$value['BookingType']:0;
                if (intval($time_schedule[$k]['src_num']) ==0 || intval($time_schedule[$k]['book_status']) != 0) {
                    $time_schedule[$k]['forbid_book'] = 1;//禁止预约
                } else {
                    $time_schedule[$k]['forbid_book'] = 0;
                }
                if ($idx == 1) {
                    // 只显示前6条
                    //break;
                }
                $idx++;
            }
        }
        $time_schedule = array2array($time_schedule);
        return $this->returnSuccess($time_schedule);
    }
    function getProDatePartSchedule($options = array()) {
        $schedule_id = $options['schedule_id'];
        $hosId = $options['hos_code'];
        $Request['MethodName'] = 'BK_GetProSchedule';
//         $Request['MethodName'] = 'BK_GetProSchedule_Test';
        $Request['DeptNo'] = $this->hos_code_map[$hosId];
        $Request['DeptSn'] = $options['dep_id'];
        $Request['DoctCode'] = $options['doc_id'];
        $Request['BeginDate'] = isset($options['from_date'])?$options['from_date']:'';
        $Request['EndDate'] = isset($options['end_date'])?$options['end_date']:'';

        if (cache('api_pro_doc_schedule' . '_' . $hosId . '_' . $options['dep_id'] . '_' . $options['doc_id'].'_'.$options['from_date'].'_'.$options['end_date'])) { // 在获取医生排班的时候进行缓存5秒getDocSchedule
            $response = cache('api_pro_doc_schedule' . '_' . $hosId . '_' . $options['dep_id'] . '_' . $options['doc_id'].'_'.$options['from_date'].'_'.$options['end_date']);
        } else {
            $response = $this->call($Request);
            if (isset($response['Result']) && $response['Result'] == 0) { // 失败
                return $this->returnErorr('未获取到分时段信息');
            }
        }
        $time_schedule = array();
        if (isset($response[0])) {
            $resArr = $response;
        } else {
            $resArr[0] = $response;
        }
        $idx = 1;
        foreach ( $resArr as $k => $value ) {
            if ($schedule_id == $value['Schedule']) { // 提取某一排班id的数据
                $time_schedule[$k]['time_type'] = $value['Shift'];
                $time_schedule[$k]['sch_date'] = substr($value['BeginDate'], 0, 10);
                $time_schedule[$k]['queue_sn'] = $value['QueueSn'];
                $time_schedule[$k]['start_time'] = substr($value['BeginDate'], 11, 5);
                $time_schedule[$k]['end_time'] = substr($value['EndDate'], 11, 5);
                $time_schedule[$k]['src_num'] = $value['CanBookNum'];
                $time_schedule[$k]['book_status'] = isset($value['BookStatus'])?$value['BookStatus']:0;
                if (intval($time_schedule[$k]['src_num']) ==0 || intval($time_schedule[$k]['book_status']) != 0) {
                    $time_schedule[$k]['forbid_book'] = 1;//禁止预约
                } else {
                    $time_schedule[$k]['forbid_book'] = 0;
                }
                if ($idx == 1) {
                    // 只显示前6条
                    //break;
                }
                $idx++;
            }
        }
        $time_schedule = array2array($time_schedule);
        return $this->returnSuccess($time_schedule);
    }
    function submitOrder($options = array()) {
        if (empty($options['pat_id'])) {
            return $this->returnErorr('请选择就诊人');
        }
        if (empty($options['jz_card'])) {
            return $this->returnErorr("此医院需要有就诊卡才能预约");
        }
        // 预约时校验数据(就诊卡,排班号,分时段序号不能为空)
        if (empty($options['schedule_id'])) {
            return $this->returnErorr("请选择您要预约的排班");
        }
        if (empty($options['queue_sn'])) { // 儿童医院是分时段预约,分时段序号不能为空,为空则说明号源已约满
            return $this->returnErorr("该时段排班已约满,请换个时段预约");
        }
        // 0231549	婴儿专家门诊（书林）        2106	婴儿专家门诊(前兴)        2326	婴儿门诊(前兴)        3015	婴儿门诊
        if (in_array($options['dep_id'], ['0231549','2106','2326','3015'])) { // 婴儿专家门诊
            // 年龄超过6个月的婴儿禁止预约
            if (dayOfDiffMonth(-6) > $options['patient_birthday']) {
                return $this->returnErorr("6个月以内的婴儿才可以预约婴儿专家门诊，请您更换诊室进行预约");
            }
        }
        static $specialdepts = [
//            '0231550',  //  呼吸科（前兴）
            '0231604',  //  儿内科（前兴）A
            '0231611',  //  儿内科（前兴）B
            '0231612',  //  儿内科（前兴）C
            '0231613',  //  儿内科（前兴）D
            '0231644',  //  儿内科（前兴）E
            '0231665',  //  儿内科（前兴）F
            '0231685',  //  儿内科（前兴）G
            'A000021',  //  儿内科（前兴）H
            'A000216',  //  儿内科（前兴）R
            '0231682',  //  儿内科（书林）A
            '0231681',  //  儿内科（书林）B
            '0231680',  //  儿内科（书林）C
//            '230302',  //  哮喘专科门诊(前兴)
//            '230319',  //   儿内科(书林)
//            '230324',  //  儿内科复诊(书林)
//            '230326',  //  儿内科(前兴)
//            '2323',  //  儿内科复诊(前兴)
//            '3001',  //  哮喘专家门诊(前兴)
//            '3003',  // 儿内科专家(前兴)
//            '3016',  //  儿内科专家(书林)
        ];
        if (in_array($options['dep_id'], $specialdepts)) {
            // 6个月以内的小孩不能预约以下科室
            if (dayOfDiffMonth(-6) < $options['patient_birthday']) {
                return $this->returnErorr("6个月以内的婴儿不可以预约此门诊科室，请您更换诊室进行预约");
            }
        }
        if($options['hos_code']=='871006'&&in_array($options['doc_id'],['282','29','136','66','4460'])){//特需门诊殷峥 、王霖、陈祝、胡玫、陈辉只看4个月以上大的婴儿
            if (dayOfDiffMonth(-4) < $options['patient_birthday']) {
                return $this->returnErorr("4个月以上的婴儿才可以预约此专家，请您更换专家进行预约");
            }
        }
        if ($options['dep_id']=='A000221') {
            if (dayOfDiffMonth(-36) < $options['patient_birthday']) {
                return $this->returnErorr("3-18岁儿童才可以预约接种。");
            }
        }
        $appointModel = model('AppointRecord');
        $record_id = $appointModel->where(['user_id'=>$options['user_id'],'jz_card'=>$options['jz_card'],'dep_id'=>$options['dep_id'],'sch_date'=>$options['sch_date'],'status'=>1])->value('id');
        if($record_id &&  (!in_array($options['dep_id'],['0231518']) || $options['hos_code'] != '871006')  ){
            return $this->returnErorr('同一科室一天只能预约一个号');
        }

        if(in_array($options['dep_id'],['A000021','A000096','2302','3014','A000039'])){
            $options['time_type'] = 1;
            if(date('H:i:s') > '12:00:00'){
                $options['time_type'] = 3;
            }
        }

        $docInfo = $this->getDoctorDetail($options['hos_code'], $options['dep_id'], $options['doc_id']);
        // 保存预约记录
        $order_no = self::genOrderNo($prefix = 'GH');
        $appointRecord['order_no'] = $order_no;
        $appointRecord['user_id'] = $options['user_id'];
        $appointRecord['patient_id'] = $options['pat_id'];
        $appointRecord['jz_card'] = $options['jz_card'];
        $appointRecord['patient_idcard'] = isset($options['patient_idcard'])?$options['patient_idcard']:'';
        $appointRecord['patient_name'] = $options['patient_name'];
        $appointRecord['patient_phone'] = $options['patient_phone'];
        $appointRecord['hos_id'] = $options['hos_code'];
        $appointRecord['dep_id'] = $options['dep_id'];
        $appointRecord['doc_id'] = $options['doc_id'];
        $appointRecord['hos_name'] = isset($options['hos_name'])?$options['hos_name']:"";
        $appointRecord['dep_name'] = isset($options['dep_name'])?$options['dep_name']:"";
        $appointRecord['doc_name'] = isset($options['doc_name'])?$options['doc_name']:"";
        $appointRecord['doc_level'] = isset($docInfo['title_name'])?$docInfo['title_name']:$docInfo['level_name'];
        $appointRecord['schedule_id'] = $options['schedule_id'];
        $appointRecord['time_type'] = $options['time_type'];
        $appointRecord['his_status'] = 1;

        $appointRecord['amt'] = isset($options['amt']) ? $options['amt'] : 0; // 缴费金额
//        if ($options['hos_code'] == '871006') {
            //$appointRecord['amt'] = $this->tx_payamt;
//        }

        $appointRecord['msg_content'] = '';
        $appointRecord['pay_status'] = -1; // -2已退款-1 不需要支付0 待支付 1 已支付
        //$appointRecord['pay_status'] = $options['hos_code'] == '871006' ? 0 : - 1; // -2已退款-1 不需要支付0 待支付 1 已支付
        $appointRecord['create_time'] = time(); // 创建时间
        $appointRecord['update_time'] = time(); // 创建时间
        $appointRecord['status'] = 0; // -1已撤销 1 预约成功 0 待生成承诺书

        $appointRecord['pre_outpatient'] = 0;//设置预问诊标识为未进行预问诊

//        $userSex = num2sex($options['patient_sex']);

        $param1 = "(ID{$options ['jz_card']} )";
        $param2 = ''; // 医院科室医生
        if (isset($appointRecord['start_time'])) {
            $param3 = $options['sch_date'] . substr($appointRecord['start_time'], 0, 5) ; // 2016-29-02上午15:00
            $param3 = date('Y年m月d日H时i分',strtotime($param3));
        } else {
            $param3 = $options['sch_date'] . num2word($options['time_type']); // 2016-29-02上午
        }
        $param4 = '严格按照预约时间提前30分钟到相应科室分诊台取号候诊，';
        $param5 = '提前到达系统不支持取号，超过30分钟取号系统将自动后延。2.如患者有发热、咳嗽、咽痛、腹泻、黄疸和皮疹等症状请先到门诊各预检分诊处进行预检。';
        $params = [$param1,$param2,$param3,$param4,$param5];

        $msgContent = '1.您' . $params[0] . '预约' . $params[1] . $params[2] . '就诊号，请' . $params[3] . $params[4];
        if(in_array($options['dep_id'],['A000021','A000096'])){
            $msgContent = '1.您' . $params[0] . '预约' . $params[1] . $params[2] . '就诊号，请至儿内 H 区预检分诊处取号候诊。' ;
        }elseif ($options['dep_id'] == 'A000129') {
            $msgContent = '1.您' . $params[0] . '预约' . $params[1] . $params[2] . '就诊号，请严格按照预约时间提前30分钟到前兴院区住院部前小广场患者及陪护核酸采集处登记处报道（开单及检测标本）。' ;
        }elseif($options['dep_id'] == 'A000091' || $options['dep_id'] == 'A000214'){
            $msgContent = '您' . $params[0] . '预约' . $params[1] . $params[2] . '就诊号，请严格按照预约时间提前30分钟到发热门诊分诊台取号候诊，提前到达系统不支持取号，超过30分钟取号系统将自动后延。' ;
        }elseif($options['dep_id'] == 'A000221') {
            $msgContent = '您' . $params[0] . '预约' . $params[1] . $params[2] . '就诊号，请严格按照预约时间提前30分钟到门诊楼五楼预防接种室分诊台取号候诊，提前到达系统不支持取号，超过30分钟取号系统将自动后延。' ;
        }

        $appointRecord['msg_content'] = $msgContent;
        $appointModel->data($appointRecord)->save();
        $options['order_id'] = $appointModel->id;

        /*if (!self::promise1($options)) {
            return $this->returnErorr('提交预约超时，请稍后重试。');
        }*/

        // h区不去医院挂号
        if(in_array($options['dep_id'],['A000021','A000096','2302','3014','A000039'])){
            $response['Result'] = 1;
            $response['BeginTime'] = date("H:i:s");
            $response['EndTime'] = date("H:i:s");
            $options['sch_date'] = date('Y-m-d');

        } else {
            $Request['MethodName'] = 'BK_DoBooking';
            $Request['ScheduleId'] = $options['schedule_id'];
            $Request['QueueSn'] = isset($options['queue_sn']) ? $options['queue_sn'] : ''; // 若不需定位预约具体时间序号则传空
            $Request['PatientID'] = $options['jz_card'];
            $Request['bookingtype'] = $this->bookingtype;
            $response = $this->call($Request);
            system_log('调用his提交预约,参数:' . json_encode($Request). ' 返回：'.json_encode($response));
        }

        // 判断预约成败
        if (!empty($response['Result']) && $response['Result'] == 1 && $response['Error'] != '该预约号已被占用, 请换其它时间试试！') {
            if (isset($options['queue_sn']) && $options['queue_sn'] != 0) { // 分时段预约
                $appointRecord['queue_sn'] = $options['queue_sn'];
                $appointRecord['start_time'] = substr($response['BeginTime'], 0, 5);
                $appointRecord['end_time'] = substr($response['EndTime'], 0, 5);
            }
            if(strstr($response['Error'], '您成功预约') ){
                $appointRecord['msg_content'] = $response['Error'];
            }
            $appointRecord['his_status'] = 0;
            $appointRecord['sch_date'] = isset($options['sch_date'])?$options['sch_date']:date('Y-m-d',strtotime($response['InvalidTime']));
            $appointRecord['time_type'] = isset($options['time_type'])?$options['time_type']:$this->timeTypeToDyt($response['ShiftCode']);
            $appointRecord['status'] = 1; // -1已撤销 1 预约成功 0 待生成承诺书
            $appointModel->save($appointRecord, ['id' => $appointModel->id]);
            $order_id = $appointModel->id;
            if ($order_id) {
                // 儿童医院特需门诊预约成功后暂不发短信和模板消息,支付成功后才发送
                //if ($options['hos_code'] == '871002' || $options['hos_code'] == '871003') {
                    // 发送短信
                    //ShortMsgJob::pushShortMsg(3018042, $mobile, $params);
                    // 发送模板消息
                    $tepParam = [];
                    $tepParam['first'] = '恭喜您成功使用微信公众号进行预约挂号';
                    // 就诊人
                    $tepParam['keyword1'] = $options['patient_name'];
                    // 患者ID号
                    $tepParam['keyword2'] = $options['jz_card'];
                    $openid = model('Patient')->getOpenid($options['pat_id']);
                    // 挂号科室
                    $tepParam['keyword3'] = $options['hos_name'].' '.$options['dep_name'];
                    // 看诊医生
                    $tepParam['keyword4'] = $docInfo['doc_name'];
                    // 就诊时间
                    if (isset($appointRecord['start_time'])) {
                        $tepParam['keyword5'] = $options['sch_date'] . substr($appointRecord['start_time'], 0, 5);
                        $time = date('Y年m月d日H时i分',strtotime($tepParam['keyword5']));
                        $tepParam['keyword5'] = $time;
                    } else {
                        $tepParam['keyword5'] = $options['sch_date'] . num2word($options['time_type']) ;
                    }
                    // 底部说明
                    //$tepParam['remark'] = "请于就诊当日此时段提前30分钟携带就诊卡到相应诊区领取排队号后等待就诊，如无法按时到院就诊，请于就诊前一天17:30前取消预约订单。\n点击详情查看就诊须知或撤销预约。";
                    $tepParam['remark'] = "请在预约就诊时间前30分钟取号，过时取号就诊时间将后延。为了维护您和其他患者的利益，无需就诊，请您在所预约就诊时间前取消预约的订单。";
                    $url = config('web_domain') . "/html/ucenter/appoint/detail.html?id={$appointModel->id}&record_type=0";
                    try {
                        $app = new Application(config('wechat'));
                        $notice = $app->notice;
                        $messageId = $notice->send([
                            'touser' => $openid,
                            'template_id' => 'dA1h8UkE8d0F_-Me3rba6syvcR-IFF-CTO9a39pk1n0',
                            'url' => $url,
                            'data' => $tepParam,
                        ]);
                    } catch (\Exception $e) {
                        system_log('发送微信模板消息失败：预约成功消息，to_openid:'.$openid);
                    }
                //}
                return $this->returnSuccess($data = ['order_no'=>$order_no,'order_id' => $appointModel->id,'type' => 2,'msg_content'=>$msgContent]);
            } else {
                system_log('预约成功,订单保存数据库失败');
                return $this->returnErorr('订单保存失败');
            }
        } else {
            $appointRecord['status'] = -1; // -1已撤销 1 预约成功 0 待生成承诺书
            $appointModel->save($appointRecord, ['id' => $appointModel->id]);
            $errMsg = isset($response['Error']) ? $response['Error'] : '';
            if ($errMsg != '该病人已预约过当天该医生的号，不能预约第2次！' && $errMsg != '该预约号已被占用, 请换其它时间试试！' && $errMsg != '6个月内的小孩不能预约该科室' ) {
                system_log("提交预约失败,参数:" . json_encode($Request) . ';返回结果:' . json_encode($response));
            }
            if(empty($errMsg)){
                $errMsg = isset($response['RESULT_DESCR']) ? $response['RESULT_DESCR'] : '';
            }

            // 发送模板消息
            $tepParam = [];
            $tepParam['first'] = '预约挂号失败原因报告：';
            // 就诊人
            $tepParam['keyword1'] = $options['patient_name'].'('.$options['jz_card'].')';
            // 患者ID号
            $tepParam['keyword2'] = isset($docInfo['doc_name'])?$docInfo['doc_name']:'';
            $openid = model('Patient')->getOpenid($options['pat_id']);
            // 挂号科室
            $tepParam['keyword3'] = $options['hos_name'].' '.$options['dep_name'];
            // 看诊医生
            $tepParam['keyword4'] = '现场缴费';
            // 就诊时间
            if (isset($appointRecord['start_time'])) {
                $tepParam['keyword5'] = $options['sch_date'] . num2word($options['time_type']) . substr($appointRecord['start_time'], 0, 5);
            } else {
                $tepParam['keyword5'] = $options['sch_date'] . num2word($options['time_type']) ;
            }
            // 底部说明
            $tepParam['remark'] = $errMsg;
            if ($errMsg != '该病人已预约过当天该医生的号，不能预约第2次！' && $errMsg != '该预约号已被占用, 请换其它时间试试！' && $errMsg != '6个月内的小孩不能预约该科室' ) {
                try {
                    $app = new Application(config('wechat'));
                    $notice = $app->notice;
                    $messageId = $notice->send([
                        'touser' => self::$DEV_OPENID,
                        'template_id' => 'wSimS3vWaQfieWlOPZHAxCgvGXp7YotEzFfEo7fdyao',
                        'data' => $tepParam,
                    ]);
                } catch (\Exception $e) {
                    system_log('发送微信模板消息失败：预约挂号失败原因报告，to_openid:'.$openid);
                }
            }
            return $this->returnErorr('提交预约失败,' . $this->replaceMsg($errMsg));
        }
    }

    protected $timeout = 7;
    public function promise1($params){

        $nowTime = time();
        $params1['user_id'] = $params['user_id'];
        $params1['patient_id'] = $params['patient_id'];
        $params1['jz_card'] = $params['jz_card'];
        $params1['id_card'] = $params['id_no'];
        $params1['relation'] = $params['rel_sign'];
        $params1['add_time'] = $nowTime;
        $params1['update_time'] = $nowTime;
        $params1['expires_time'] = $nowTime + (86400 * $this->timeout);

        $post_data['id'] = $params1['jz_card'].'-'.$params1['patient_id'].'-'.$params1['add_time'];
        $post_data['name_sign'] = $params['name_sign'];
        $result = http_postjson_request('http://172.17.121.61:8289/api/v1/ertong/name/base642img', json_encode($post_data), 30);
        if ($result == false) {
            return false;
        }

        $res = json_decode($result, true);
        if(false == $res || empty($res['name_sign'])){
            return false;
        }
        $params1['sign_url'] = $res['name_sign'];
        // 成功条数
        $num = Db::name('patient_covid19_confirm')->insert($params1);
        return $num;
    }

    function promise($params) {
        $Patient = new \app\common\model\Patient();
        $info = $Patient->getDetailWithUserId($params['user_id'], $params['patient_id']);

        $post_data['id'] = $params['order_id'];
        $post_data['name'] = $params['patient_name'];
        $post_data['birth'] = date("Ymd",strtotime($info['patient_birthday']));
        $post_data['dep_name'] = $params['dep_name'];
        $post_data['id_card'] = $params['id_no'];
        $post_data['jz_card'] = $params['jz_card'];
        $post_data['sex'] = $params['sex'];
        $post_data['name_sign'] = $params['name_sign'];
        $post_data['rel_sign'] = $params['rel_sign'];

        $result = http_postjson_request('http://172.17.121.61:8289/api/v1/ertong/base642img', json_encode($post_data), 30);
        if ($result == false) {
            return false;
        }
        $result = json_decode($result, true);
        $promiseModel = new PromiseData();
        $promiseData['appoint_record_id'] = $params['order_id'];
        $promiseData['patient_name'] = $params['patient_name'];
        $promiseData['patient_birth'] = date("Ymd",strtotime($info['patient_birthday']));
        $promiseData['appoint_dep'] = $params['dep_name'];
        $promiseData['id_card_no'] = $params['id_no'];
        $promiseData['jz_card'] = $params['jz_card'];
        $promiseData['bed_num'] = '';
        $promiseData['patient_sex'] = $params['sex'];
        $promiseData['name_sign_url'] = $result['name_sign'];
        $promiseData['relation_sign_url'] = $result['rel_sign'];
        $promiseData['sign_date'] = time();

        $promiseModel->data($promiseData)->save();
        return $promiseData;
    }

    /*
     * 支付订单,微信支付成功后修改订单的状态
     */
    function payOrder($options = array()) {
        $appointRecordModel = new AppointRecord();
        $payOrder = $appointRecordModel->getAppointRecordPayInfo($options['order_id']);
        $orderNo = $payOrder['order_no'];
        system_log("----------开始处理订单[$orderNo]支付-------------");
        // 获取医生信息
        $docInfo = $this->getDoctorDetail($payOrder['hos_id'], $payOrder['dep_id'], $payOrder['doc_id']);

        system_log("开始更新数据库状态");
        $param1 = $payOrder['patient_name'];
        $param2 = $docInfo['hos_name'] . $docInfo['dep_name'] . $docInfo['doc_name']; // 医院科室医生
        $param3 = $payOrder['sch_date'] . num2word($payOrder['time_type']) . substr($payOrder['start_time'], 0, 5) . '-' . substr($payOrder['end_time'], 0, 5); // 2016-29-02上午15:00
        $param4 = '此时段提前30分钟携带就诊卡到门诊楼三楼特需门诊处就诊';
        $param5 = '在就诊前一天撤销预约';
        $mobile = $payOrder['patient_phone'];
        $params = [$param1,$param2,$param3,$param4,$param5];

        // $content = 恭喜您%s已成功预约%s，就诊时间%s，请于就诊当日%s。如无法前往请%s。;
        $msgContent = '恭喜您' . $params[0] . '已成功预约' . $params[1] . '，就诊时间' . $params[2] . '，请于就诊当日' . $params[3] . '。如无法前往请' . $params[4] . '。';

        $data['id'] = $payOrder['id'];
        $data['pay_status'] = 1;
        $data['his_status'] = 0;
        $data['msg_content'] = $msgContent;

        $payResult = $appointRecordModel->appointRecordUpdate($data);
        if ($payResult) {
            system_log("数据库状态状态更新成功");
            // 发送短信
            //ShortMsgJob::pushShortMsg(3018042, $mobile, $params);
            // 发送模板消息
            $openid = $payOrder['openid'];
            $tepParam['hos_name'] = $docInfo['hos_name'];
            $tepParam['dep_name'] = $docInfo['dep_name'];
            $tepParam['doc_name'] = $docInfo['doc_name'];
            $tepParam['start_time'] = $payOrder['sch_date'] . num2word($payOrder['time_type']) . substr($payOrder['start_time'], 0, 5);
            $url = config('web_domain') . "/html/ucenter/appoint/detail.html?id={$payOrder['id']}&record_type=0";
            //WxTemplateMsgJob::pushWxTemplateMsg($type = 1, $openid, $tepParam, $url);
            /*
            $app = new Application(config('wechat'));
            $notice = $app->notice;
            $messageId = $notice->send([
                'touser' => $openid,
                'template_id' => 'template-id',
                'url' => $url,
                'data' => $tepParam,
            ]);
            */
            return $this->returnSuccess('支付成功');
        } else {
            system_log("数据库状态状态更新失败");
            return $this->returnErorr('订单状态修改失败');
        }
    }

    /*
     * 撤销预约
     * @param array $options['order_id'] 表达式参数
     * @return array 撤销结果
     * <AUTHOR>
     */
    function cancelOrder($options = array()) {
        $cancel_channel = $options['cancel_channel']; // 取消预约渠道
        $appointRecordModel = new AppointRecord();
        $appointRecord = $appointRecordModel->where(['id' => $options['order_id'],'status' => 1])->find();
        if (! $appointRecord) {
            return $this->returnErorr('未找到订单,或已取消');
        }
        $appointRecordData = $appointRecord->getData();
        $appointRecordData['cancel_msg'] = $options['cancel_msg'];
        if ($appointRecordData['pay_status'] == 1) { // 已支付的订单
            return $this->cancelPayOrderFun($appointRecordData, $cancel_channel);
        } else {
            return $this->cancelUnPayOrderFun($appointRecordData, $cancel_channel);
        }
    }

    /*
     * 撤销已支付订单的方法
     * @param array $options 表达式参数
     * @return array 撤销结果
     * <AUTHOR>
     */
    private function cancelPayOrderFun($options = array(), $cancel_channel) {
        $orderNo = $options['order_no'];
        if (config('app_debug')) {
            system_log("----------开始处理订单[$orderNo]退号-------------");
        }
        $appointRecordModel = new AppointRecord();
        $payOrder = $appointRecordModel->getAppointRecordPayInfo($options['id']);

        $Request['MethodName'] = 'BK_DoCancleBook';
        $Request['ScheduleId'] = $payOrder['schedule_id']; // 预约排班号
        $Request['QueueSn'] = $payOrder['queue_sn']; // 预约序号
        $Request['Opera'] = $this->opera; // 操作员ID
        $Request['PatientID'] = $options['jz_card']; // 就诊人id
        $cancelTime = time(); // 记录撤销预约的时间
        $response = $this->call($Request);
        if (config('app_debug')) {
            system_log("开始调用his接口撤销预约, 参数：" . json_encode($Request).' 返回：'.json_encode($response));
        }
        if (isset($response['Result']) && $response['Result'] == 1) {
            // 调用微信退款
            if (config('app_debug')) {
                system_log("调用his接口退号成功,开始调用微信退款接口退款,订单号[$orderNo],退款金额:" . $payOrder['real_pay_money']);
            }
            $back_res = $this->wxRefund($payOrder['order_no'], $payOrder['real_pay_money'], 0, $payOrder['sub_mch_id']);
            if (isset($back_res['result_code']) && $back_res['result_code'] == 'SUCCESS') { // 退款成功
                if (config('app_debug')) {
                    system_log("调用微信退款接口成功,开始更新数据库表状态");
                }
                $data['id'] = $payOrder['id'];
                $data['status'] = - 1;
                $data['pay_status'] = - 2;
                $data['his_status'] = 0;
                $data['cancel_time'] = $cancelTime;
                $data['cancel_msg'] = $options['cancel_msg'];

                $cancelResult = $appointRecordModel->appointRecordUpdate($data);
                if ($cancelResult) {
                    // 发送撤销模板消息
                    if (config('app_debug')) {
                        system_log("数据库状态更新成功,开始发送模板消息");
                    }
                    $template_url = config('web_domain') . "/html/ucenter/appoint/detail.html?id={$payOrder['id']}&record_type=0";
                    $sendWechatData = [];
                    $sendWechatData['first'] = "您好，您预约的号源已经成功取消。取消号源信息如下：";
                    // 就诊人姓名
                    $sendWechatData['keynote1'] = $payOrder['patient_name'];
                    // 就诊时间
                    $sendWechatData['keynote2'] = $payOrder['sch_date'] . num2word($payOrder['time_type']);
                    // 就诊医生
                    $sendWechatData['keynote3'] = $payOrder['doc_name'];
                    // 就诊医院
                    $sendWechatData['keynote4'] = $this->hos_name_map[$payOrder['hos_id']];

                    if (isset($options['cancel_msg'])) {
                        $sendWechatData['remark'] = "取消原因：".$options['cancel_msg'].'。如已缴费，退款将在7个工作日内通过支付渠道返还，祝您和家人身体健康';
                    } else {
                        $sendWechatData['remark'] = "如已缴费，退款将在7个工作日内通过支付渠道返还，祝您和家人身体健康";
                    }
                    //$sendWechatData['remark'] = "如已缴费，退款将在7个工作日内通过支付渠道返还，祝您身体健康";
//                     $sendWechatData = ['patient_name' => $payOrder['patient_name'],'sch_date' => $payOrder['sch_date'] . num2word($payOrder['time_type']),'doc_name' => $payOrder['doc_name'],'hos_name' => $this->hos_name_map[$payOrder['hos_id']]];
                    $openId = $payOrder['openid'];
                    try {
                        $app = new Application(config('wechat'));
                        $notice = $app->notice;
                        $messageId = $notice->send([
                            'touser' => $openId,
                            'template_id' => 's7Byfl9cyXm1ZCLfPU0fj-d53805oiRutNLvIBBA1Mo',
                            'url' => $template_url,
                            'data' => $sendWechatData,
                        ]);
                    } catch (\Exception $e) {
                        system_log('发送微信模板消息失败：取消预约，to_openid:'.$openId);
                    }

                    // 模板消息添加到队列
                    //WxTemplateMsgJob::pushWxTemplateMsg($type = 2, $openId, $sendWechatData, $template_url);
                    if (config('app_debug')) {
                        system_log("-------------退号成功,订单号[$orderNo]----------------");
                    }
                    return self::returnSuccess(null, '退号成功');
                } else {
                    system_log("-------------退号失败,数据库状态更新失败,订单号[$orderNo]----------------");
                    return self::returnSuccess(null, '退号失败');
                }
            } else {
                $errMsg = isset($back_res['err_code_des']) ? $back_res['err_code_des'] : '调用微信退款失败'; // 错误消息
                system_log("调用微信退款接口失败,订单号[$orderNo]:" . json_encode($back_res));
                // his撤销成功,微信退款失败
                $data['id'] = $payOrder['id'];
                $data['status'] = - 1; // 1:已撤销
                $data['his_status'] = self::TRADE_CANCEL_201; // 2:his退款成功,微信退款失败
                $data['cancel_time'] = $cancelTime;
                $data['cancel_msg'] = $options['cancel_msg'];
                $data['mark'] = $errMsg;
                $appointRecordModel->appointRecordUpdate($data);

                // 记录错误日志
                $errInfo = '调微信退款失败,订单号[' . $orderNo . '],微信返回:' . $errMsg;
                $errData['order_no'] = $orderNo;
                $errData['order_event'] = self::TRADE_TYPE_CANCEL; // 撤销预约;
                $errData['event_type'] = $cancel_channel; // 撤销渠道;
                $errData['event_info'] = $errInfo;
                $exceptionLogModel = new ExceptionLog();
                $exceptionLogModel->data($errData)->save();

                // 微信退款失败,给开发者发送信息
                $sendWechatData['first'] = "取消预约失败原因报告：";
                // 就诊人姓名
                $sendWechatData['keynote1'] = $payOrder['patient_name'];
                // 就诊时间
                $sendWechatData['keynote2'] = $payOrder['sch_date'] . num2word($payOrder['time_type']);
                // 就诊医生
                $sendWechatData['keynote3'] = $payOrder['doc_name'];
                // 就诊医院
                $sendWechatData['keynote4'] = $this->hos_name_map[$payOrder['hos_id']];
                // 故障信息
                $sendWechatData['remark'] = $errInfo;
                try {
                    $app = new Application(config('wechat'));
                    $notice = $app->notice;
                    $messageId = $notice->send([
                        'touser' => self::$DEV_OPENID,
                        'template_id' => 's7Byfl9cyXm1ZCLfPU0fj-d53805oiRutNLvIBBA1Mo',
                        'data' => $sendWechatData,
                    ]);
                } catch (\Exception $e) {
                    system_log('发送微信模板消息失败：调用微信退款失败，to_openid:'.self::$DEV_OPENID);
                }
                return self::returnErorr('撤销失败:调用微信退款接口失败,' . $this->replaceMsg($errMsg));
            }
        } else {
            $errMsg = isset($response['Error']) ? $response['Error'] : '调用his接口取消预约失败'; // 错误消息
            system_log("调用his接口取消预约失败,订单号[$orderNo]:" . $errMsg);

            // 微信退款失败,给开发者发送信息
            $sendWechatData['first'] = "取消预约失败原因报告：";
            // 就诊人姓名
            $sendWechatData['keynote1'] = $payOrder['patient_name'];
            // 就诊时间
            $sendWechatData['keynote2'] = $payOrder['sch_date'] . num2word($payOrder['time_type']);
            // 就诊医生
            $sendWechatData['keynote3'] = $payOrder['doc_name'];
            // 就诊医院
            $sendWechatData['keynote4'] = $this->hos_name_map[$payOrder['hos_id']];
            // 故障信息
            $sendWechatData['remark'] = '调his取消预约失败,订单号[' . $orderNo . '],his返回:' . $errMsg;
            try {
                $app = new Application(config('wechat'));
                $notice = $app->notice;
                $messageId = $notice->send([
                    'touser' => self::$DEV_OPENID,
                    'template_id' => 's7Byfl9cyXm1ZCLfPU0fj-d53805oiRutNLvIBBA1Mo',
                    'data' => $sendWechatData,
                ]);
            } catch (\Exception $e) {
                system_log('发送微信模板消息失败：取消预约失败败，to_openid:'.self::$DEV_OPENID);
            }
            // 记录错误日志
            $errData['order_no'] = $orderNo;
            $errData['order_event'] = self::TRADE_TYPE_CANCEL; // 撤销预约;
            $errData['event_type'] = $cancel_channel; // 撤销渠道;
            $errData['event_info'] = '调his取消预约失败,订单号[' . $orderNo . '],his返回:' . $errMsg;
            $exceptionLogModel = new ExceptionLog();
            $exceptionLogModel->data($errData)->save();
            return self::returnErorr('撤销失败,调用医院接口失败,' . $this->replaceMsg($errMsg));
        }
    }

    /*
     * 撤销预约的方法
     * @param array $options 表达式参数
     * @return array 撤销结果
     * <AUTHOR>
     */
    private function cancelUnPayOrderFun($options = array(), $cancel_channel = 1) {
        $orderNo = $options['order_no'];
        if (config('app_debug')) {
            system_log("----------开始处理订单[$orderNo]撤销预约-------------");
        }
        $appointRecordModel = new AppointRecord();
        $Request['MethodName'] = 'BK_DoCancleBook';
        $Request['ScheduleId'] = $options['schedule_id']; // 预约排班号
        $Request['QueueSn'] = $options['queue_sn']; // 预约序号
        $Request['Opera'] = $this->opera; // 操作员ID
        $Request['PatientID'] = $options['jz_card']; // 就诊人id
        $response = $this->call($Request);
        system_log("开始调用his接口撤销预约, 参数：" . json_encode($Request).' 返回：'.json_encode($response));

        if (isset($response['Result']) && $response['Result'] == 1) { // 撤销成功
            $data['id'] = $options['id'];
            $data['status'] = - 1;
            $data['cancel_time'] = time();
            $data['cancel_msg'] = $options['cancel_msg'];

            $cancelResult = $appointRecordModel->appointRecordUpdate($data);
            $openId = model('Patient')->getOpenid($options['patient_id']);
            // 发送撤销模板消息
            $template_url = config('web_domain') . "/html/ucenter/appoint/detail.html?id={$options['id']}&record_type=0";
            $sendWechatData['first'] = "亲爱的家长您好，您预约的号源已经成功取消。取消号源信息如下：";
            if ($cancel_channel == 1) { // 用户手动撤销预约

            } elseif ($cancel_channel == 2) { // 定时任务自动撤销预约
                $sendWechatData['first'] = "亲爱的家长您好，您有超时未支付的预约订单，系统自动释放号源：";
            }

            // 就诊人姓名
            $sendWechatData['keynote1'] = $options['patient_name'];
            // 就诊时间
            $sendWechatData['keynote2'] = $options['sch_date'] . num2word($options['time_type']);
            // 就诊医生
            $sendWechatData['keynote3'] = $options['doc_name'];
            // 就诊医院
            $sendWechatData['keynote4'] = $this->hos_name_map[$options['hos_id']];
            if (isset($options['cancel_msg'])) {
                $sendWechatData['remark'] = "取消原因：".$options['cancel_msg'].'。祝您和家人身体健康';
            } else {
                $sendWechatData['remark'] = "祝您和家人身体健康";
            }
            try {
                $app = new Application(config('wechat'));
                $notice = $app->notice;
                $messageId = $notice->send([
                    'touser' => $openId,
                    'template_id' => 's7Byfl9cyXm1ZCLfPU0fj-d53805oiRutNLvIBBA1Mo',
                    'url' => $template_url,
                    'data' => $sendWechatData,
                ]);
            } catch (\Exception $e) {
                system_log('发送微信模板消息失败：取消无需支付预约，to_openid:'.$openId);
            }
            return self::returnSuccess(null, '撤销成功');
        } else {
            $errMsg = isset($response['Error']) ? $response['Error'] : '调his撤销预约失败';
            if ($response['Error'] != '当前所选的登记信息不能取消，可能已挂号或分诊！') {
                system_log("订单号[$orderNo],撤销预约失败,调用医院接口撤销失败," . $errMsg);
            }

            $sendWechatData['first'] = "取消预约失败原因报告：";
            // 就诊人姓名
            $sendWechatData['keynote1'] = $options['patient_name'];
            // 就诊时间
            $sendWechatData['keynote2'] = $options['sch_date'] . num2word($options['time_type']);
            // 就诊医生
            $sendWechatData['keynote3'] = $options['doc_name'];
            // 就诊医院
            $sendWechatData['keynote4'] = $this->hos_name_map[$options['hos_id']];
            // 故障信息
            $sendWechatData['remark'] = "订单号[$orderNo],撤销预约失败,调用医院接口撤销失败," . $errMsg;
            if ($response['Error'] != '当前所选的登记信息不能取消，可能已挂号或分诊！') {
                try {
                    $app = new Application(config('wechat'));
                    $notice = $app->notice;
                    $messageId = $notice->send([
                        'touser' => self::$DEV_OPENID,
                        'template_id' => 's7Byfl9cyXm1ZCLfPU0fj-d53805oiRutNLvIBBA1Mo',
                        'data' => $sendWechatData,
                    ]);
                } catch (\Exception $e) {
                    system_log('发送微信模板消息失败：取消无需支付预约失败，to_openid:'.self::$DEV_OPENID);
                }
                // 记录错误日志
                $errData['order_no'] = $orderNo;
                $errData['order_event'] = self::TRADE_TYPE_CANCEL; // 撤销预约;
                $errData['event_type'] = $cancel_channel; // 撤销渠道;
                $errData['event_info'] = $errMsg;
                $exceptionLogModel = new ExceptionLog();
                $exceptionLogModel->data($errData)->save();
            }
            return self::returnErorr('撤销失败:' . $this->replaceMsg($errMsg));
        }
    }

    // 儿童线上卡类型转医院his卡类型
    function online2HisIdCardType($idcard_type){
        if ($idcard_type == 0)return "01";
        if ($idcard_type == 1)return "08";
        if ($idcard_type == 2)return "02";
        if ($idcard_type == 3)return "03";
        if ($idcard_type == 4)return "06";
        if ($idcard_type == 5)return "07";
        if ($idcard_type == 9)return "99";
        return "01";
    }

    public function syncPatientInfo2His($patientIdCard,$medicalCardNumber) {
        $Request['MethodName'] = 'MZ_GETIDCard';
        $Request['ID_card'] = $patientIdCard;
        $Request['PatientID'] = $medicalCardNumber;
//        dump($Request);
        $res = $this->call($Request);
        if (isset($res['Result']) && $res['Result'] == 0) {
            return true;
        }
        return false;
    }

    /*
     * 新患者注册
     */
    public function regPatient($options = array()) {

        $idcard_type = "";
        if(!empty($options['patient_idcard'])){
            $idcard_type = $this->online2HisIdCardType($options['idcard_type']);
        }

        $Request['MethodName'] = 'MZ_RegisterPat';
//        $Request['MethodName'] = 'MZ_RegisterPat_test';
        $Request['PatName'] = $options['patient_name']; // 患者姓名
        $Request['PatSex'] = $options['patient_sex'] == 1 ? '男' : '女'; // 患者性别
//         $Request['PatSex'] = $options['patient_sex']; // 患者性别
        $Request['document_type'] = $idcard_type; // 证件类型 如：身份证   出生证
        $Request['social_code'] = $options['patient_idcard']; // 证件号  如：身份证号   出生证号
        $Request['parent_social'] = $options['guarantee_idcard']; // 监护人身份证
        $Request['Birthday'] = $options['patient_birthday']; // 患者生日
        $Request['RelationTel'] = $options['patient_phone']; // 监护人联系电话
        $Request['Nation'] = isset($options['patient_nation'])?$options['patient_nation']:'其他';
        $Request['Address'] = isset($options['patient_address'])?$options['patient_address']:'待完善';
                                     // 与患者关系
        if (isset($options['patient_relation'])) {
            $Request['RelationName'] = $options['patient_relation'];
        } else {
            $Request['RelationName'] = '其他亲属';
        }
        $Request['Relation'] = $options['guarantee_name']; // 监护人姓名
//        dump($Request);
        system_log("create patient to his,params:".json_encode($Request));
        $res = $this->call($Request);
//        dump($res);die;

        if (isset($res['Result']) && $res['Result'] == 0) {
            try {
                $errInfo = '调his注册就诊人失败!参数：[' . json_encode($Request) . '],his接口返回:' . json_encode($res);
                system_log($errInfo);
            } catch (\Exception $e) {
            }
            return self::returnErorr($this->replaceMsg($res['Error']));
        } elseif (empty($res['PatientID'])) {
            return self::returnErorr('医院未返回就诊卡ID');
        }

        $jzCard = $res['PatientID'];
        $data['jz_card'] = $jzCard; // 门诊号码，病人患者ID号
        return self::returnSuccess($data);
    }

    /*
     * 卡号绑定
     */
    public function bindCard($options = array()) {
        $Request['MethodName'] = 'MZ_BingCard';
        $Request['PatName'] = $options['patient_name']; // 就诊人姓名
        $Request['PatientID'] = $options['jz_card']; // 就诊卡
        $Request['RelationTel'] = $options['patient_phone']; // 建档时所留的手机号
        $res = $this->call($Request);
        if (isset($res['Result']) && $res['Result'] == 1) {
//             // 儿童医院三个院区共用就诊卡,添加就诊卡要同时维护添加
//             $patientCard = new PatientCard();
//             $cardlist = [
//                 ['hos_id' => '871002', 'hos_name'=>$this->hos_name_map['871002'], 'patient_id' => $options['pat_id'],'jz_card' => $options['jz_card']], // 前兴院区
//                 ['hos_id' => '871003', 'hos_name'=>$this->hos_name_map['871003'], 'patient_id' => $options['pat_id'],'jz_card' => $options['jz_card']], // 书林院区
//                 ['hos_id' => '871006', 'hos_name'=>$this->hos_name_map['871006'], 'patient_id' => $options['pat_id'],'jz_card' => $options['jz_card']] // 前兴特需门诊
//             ];
//             foreach ($cardlist as $card) {
//                 $rs = $patientCard->where($card)->find();
//                 if (!$rs) {
//                     $patientCard->insert($card);
//                 }
//             }

//             $data['jz_card'] = $options['jz_card'];
            return self::returnSuccess([]);
        } else {
            return self::returnErorr($this->replaceMsg($res['Error']));
        }
    }

    public function getMzZhiYin($options = array()) {
        $Request['MethodName'] = 'MZ_GetGuideInfo';
        $Request['PatientID'] = $options['jz_card']; // 就诊卡
        $Request['l_account_sn'] = $options['bill_id']; // 就诊次数
        $Request['mobile'] = $options['mobile']; // 就诊次数
        system_log("指引信息入参:".json_encode($Request));
        $res = $this->call($Request);
        system_log("指引信息出参:".json_encode($res));
        if(!empty($res['info'])) {
            return $res['info'];
        }
        return '';
    }

    public function getBookRecord($options = array()) {
        $Request['MethodName'] = 'MZ_GetBookRecord';
        $Request['PatientID'] = $options['jz_card']; // 就诊卡
        $res = $this->call($Request);
        if (isset($res['Result']) && $res['Result'] == 1) {
            // 单条记录
            return self::returnSuccess([$res]);
        } else if (isset($res['Error'])) {
            // 没有记录
            return self::returnErorr($res['Error']);
        } else if (is_array($res)) {
            // 多条记录
            return self::returnSuccess($res);
        } else {
            return self::returnErorr('未找到相应记录');
        }
    }

    public function upHisTxVuid($options = array()) {
        $Request = [];
        $Request['MethodName'] = 'mz_patient_vuid';
        $Request['p_id'] = $options['p_id'];
        $Request['cup_id'] = $options['cup_id'];
        $Request['card'] = $options['card'];


        /*$Request['p_id'] = '17870350';
        $Request['cup_id'] = '424B23937BA1313CF16EA19000CD337CB329D821A049518AAA676D025A83C4D3';
        $Request['card'] = '371425199212019411';*/
        $res = $this->call($Request);
        system_log('更新his用户vuid信息(患者ID：'.$Request['p_id'].')返回:' .json_encode($res));
        return $res;
//        dump($res);
    }

    /*
     * 获取病人资料信息
     */
    public function searchPatientCard($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
            $options['p_id'] = $this->hos_code;
        }
        $Request = [];
        $Request['MethodName'] = 'BK_GetPatInfo';
        // $DeptNo = $this->hos_code_map[$options['hos_code']];
        // $Request['DeptNo'] = $DeptNo;
        if (isset($options['patient_phone']) && $options['patient_phone']) {
            $Request['FindType'] = 7; // 查询类型:联系电话
            $Request['FindNo'] = $options['patient_phone'];
        } else if (isset($options['patient_idcard']) && $options['patient_idcard']) {
            $Request['FindType'] = 6; // 查询类型:身份证
            $Request['FindNo'] = $options['patient_idcard'];
        } else if (isset($options['jz_card']) && $options['jz_card']) {
            $Request['FindType'] = 3; // 查询类型:门诊ID
            $Request['FindNo'] = $options['jz_card'];
        }
        $res = $this->call($Request);
        if ($Request['FindType'] == 7) {
            // 电话方式查询的可能有多条记录
            if (isset($res['Result']) ) {
                // 只返回一条记录
                if ($res['Result'] == 1) {
                    $data = [];
                    $data['patient_name'] = $res['PatientName'];
                    $data['patient_sex'] = $res['Sex'] == "男" ? 1 : 2;
                    $data['patient_birthday'] = $res['Birthday'];
                    $data['patient_nation'] = $res['Nation'];
                    $data['patient_address'] = $res['Address'];
                    $data['guarantee_name'] = isset($res['Relation'])?$res['Relation']:'';
                    $data['patient_relation'] = isset($res['RelationName'])?$res['RelationName']:'其他亲属';
                    $data['patient_phone'] = $res['RelationTel'];
                    $data['guarantee_phone'] = $res['RelationTel'];
                    $data['patient_idcard'] = isset($res['social_code'])?$res['social_code']:''; // 患者身份证
                    $data['guarantee_idcard'] = isset($res['parent_social'])?$res['parent_social']:''; // 监护人身份证
                    $data['jz_card'] = $res['PatientID'];
                    return self::returnSuccess([$data]);
                } else {
                    return self::returnErorr($this->replaceMsg($res['Error']));
                }
            } else {
                // 多条形式的
                $result_list = [];
                foreach ($res as $result) {
                    if (isset($result['Result']) && $result['Result'] == 1) {
                        $data = [];
                        $data['patient_name'] = $result['PatientName'];
                        $data['patient_sex'] = $result['Sex'] == "男" ? 1 : 2;
                        $data['patient_birthday'] = $result['Birthday'];
                        $data['patient_nation'] = isset($result['Nation'])?$result['Nation']:'';
                        $data['patient_address'] = $result['Address'];
                        $data['guarantee_name'] = isset($result['Relation'])?$result['Relation']:'';
                        $data['patient_relation'] = $result['RelationName'];
                        $data['patient_phone'] = $result['RelationTel'];
                        $data['guarantee_phone'] = $result['RelationTel'];
                        $data['patient_idcard'] = isset($result['social_code'])?$result['social_code']:''; // 患者身份证
                        $data['guarantee_idcard'] = isset($result['parent_social'])?$result['parent_social']:''; // 监护人身份证
                        $data['jz_card'] = $result['PatientID'];
                        $result_list[] = $data;
                    }
                }
                if ($result_list) {
                    return self::returnSuccess($result_list);
                } else {
                    return self::returnErorr("没有查询到相关就诊人信息");
                }
            }
        } else {
            // 不是电话查询的
            if(!isset($res['Result']) && isset($res[0])) {
                $res = $res[0];
            }
            if (isset($res['Result']) && $res['Result'] == 1) {
                if (isset($options['patient_phone']) && $options['patient_phone']) {
                    if (isset($res['Result']['PatientData']) && $res['Result']['PatientData']) {
                        $patientData = $res['Result']['PatientData'];
                        return self::returnSuccess($patientData);
                    } else {
                        return self::returnErorr($this->replaceMsg($res['Error']));
                    }
                } else {
                    $data = [];
                    $data['patient_name'] = $res['PatientName'];
                    if (isset($res['Sex'])) {
                        $data['patient_sex'] = $res['Sex'] == "男" ? 1 : 2;
                    } else {
                        $data['patient_sex'] = 0;
                    }

                    $data['patient_birthday'] = $res['Birthday'];
                    $data['patient_nation'] = isset($res['Nation'])?$res['Nation']:'';
                    $data['patient_address'] = $res['Address'];
                    $data['guarantee_name'] = isset($res['Relation'])?$res['Relation']:'';
                    $data['patient_relation'] = isset($res['RelationName'])?$res['RelationName']:'其他';
                    $data['patient_phone'] = isset($res['RelationTel'])?$res['RelationTel']:'';
                    $data['guarantee_phone'] = isset($res['RelationTel'])?$res['RelationTel']:'';
                    $data['patient_idcard'] = isset($res['social_code'])?$res['social_code']:''; // 患者身份证
                    $data['guarantee_idcard'] = isset($res['parent_social'])?$res['parent_social']:''; // 监护人身份证
                    $data['jz_card'] = $res['PatientID'];
                    return self::returnSuccess($data);
                }
            } else {
                return self::returnErorr($this->replaceMsg($res['Error']));
            }
        }
    }

    // 获取分诊排队信息
    public function wait($options = array()) {
        // 获取病人的就诊卡
        $param['hos_id'] = $options['hos_id'];
        $param['patient_id'] = $options['patient_id'];
        $UserApi = new UserApi();
        $result = $UserApi->getPatientAndCard($param);
        $result = json_decode($result, true);
        if ($result['code'] != 1)
            return ['code' => 0,'msg' => '获取就诊人信息失败','data' => ''];
        if (empty($result['data']['jz_card']))
            return ['code' => 0,'msg' => '该就诊人在此医院没有就诊卡','data' => ''];

        $Request['MethodName'] = 'BK_GetDistInfo';
        $Request['PatientID'] = $result['data']['jz_card'];
        $res = $this->call($Request);
        $data = null;
        if (isset($res['Result']) && $res['Result'] == 1) {
            $resArr = array();
            if (isset($res[0])) {
                $resArr = $res;
            } else {
                $resArr[0] = $res;
            }
            foreach ( $resArr as $k => $v ) {
                $data[$k]['jz_card'] = $result['data']['jz_card']; // 就诊卡（病历号）
                $data[$k]['patient_name'] = $result['data']['patient_name']; // 就诊人姓名
                $data[$k]['depart_name'] = isset($v['VisitDeptName']) ? $v['VisitDeptName'] : ''; // 科室名
                $data[$k]['doctor_name'] = $v['VisitDoctName']; // 医生名
                $data[$k]['see_doctor_date'] = isset($v['VisitDate']) ? $v['VisitDate'] : date('Y-m-d'); // 看诊日期
                $data[$k]['see_doctor_time_type'] = $this->timeTypeToStr($v['Shift']); // 看诊时间类型
                $data[$k]['my_see_no'] = $v['QueueSN']; // 排队编号
                $data[$k]['now_see_num'] = $v['PatWaitCount']; // 前面候诊排队人数
                $data[$k]['wait_time'] = $v['WaitTime']; // 预计等待时间(单位分钟)

                $data[$k]['now_see_no'] = ''; // 当前编号
                $data[$k]['appoint_no'] = ''; // 挂号流水号(支付成功返回的)
                $data[$k]['doctor_level'] = ''; // 医生级别
            }
        }
        return $this->returnSuccess($data);
    }

    public function getMzPayedInfo($options = array()) {
        $infoList = [];
        $jz_cards = [];
        if (empty($options['jz_cards'])) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }
        foreach ($options['jz_cards'] as $value) {
            $jz_cards[$value['jz_card']] = $value['pat_id'];
        }

        if (empty($jz_cards)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }
        foreach ($jz_cards as $jz_card => $patient_id) {
            $Request['MethodName'] = 'Mz_getPatientID';
            $Request['PatientID'] = $jz_card;
            $Request['Times'] = $options['date'];
            $res = $this->call($Request);
            if (config('app_debug')) {
                system_log('调用his查询门诊缴费单信息（指引单前置）接口(患者ID：'.$jz_card.')返回:' .json_encode($res));
            }
            $resArr = array();
            if (isset($res[0])) {
                $resArr = $res;
            } else {
                $resArr[0] = $res;
            }
            foreach ( $resArr as $k => $v ) {
                if (isset($v['Result']) && $v['Result'] == 1) {
                    $payinfo = [];
                    $payinfo['dept'] = $v['dept'];
                    $payinfo['doctor'] = $v['doctor'];
                    $payinfo['amt'] = $v['je'];
                    $payinfo['TIMES'] = $v['TIMES'];
                    $payinfo['ACCOUNT'] = $v['ACCOUNT'];
                    $payinfo['jz_card'] = $jz_card;
                    $infoList[] = $payinfo;
                }
            }
        }
        if ($infoList) {
            system_log('调用his查询门诊缴费单信息（指引单前置）接口返回多条记录:' .json_encode($res).' 结果：'.json_encode($infoList));
            return $this->returnSuccess($infoList);
        } else {
            return $this->returnErorr('未查到缴费记录');
        }
    }

    public function getMzPayedInfoDetail($options = array())
    {
        $Request['MethodName'] = 'Mz_getMsg';
        $Request['PatientID'] = $options['jz_card'];
        $Request['Times'] = $options['times'];
        $Request['ACCOUNT'] = $options['account'];
        $res = $this->call($Request);
        if (config('app_debug')) {
            system_log('调用his查询门诊指引单接口(患者ID：' . $options['jz_card'] . ')返回:' . json_encode($res));
        }
        $resArr = array();
        if (isset($res[0])) {
            $resArr = $res;
        } else {
            $resArr[0] = $res;
        }
        system_log('调用his查询门诊指引单接口记录:' . json_encode($res) . ' 结果：' . json_encode($resArr));
        return $this->returnSuccess($resArr);
    }

    // 获取门诊待缴费清单
    public function getMzUnPayList($options = array()) {
        $unPayList = [];
        $jz_cards = [];
        if (empty($options)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }
        foreach ($options as $value) {
            $jz_cards[$value['jz_card']] = $value['pat_id'];
        }

        if (empty($jz_cards)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }

        foreach ($jz_cards as $jz_card => $patient_id) {
            //TODO MI test
            if ($jz_card == '17339991' || $jz_card == '18936182') {
                $Request['MethodName'] = 'MZ_GetUnPayList2024';
            } else{
                $Request['MethodName'] = 'MZ_GetUnPayList';
            }
            $Request['PatientID'] = $jz_card;
            $res = $this->call($Request);
//            dump($res);
            if (config('app_debug')) {
                system_log('调用his待缴费接口(患者ID：'.$jz_card.')返回:' .json_encode($res));
            }
//             if (isset($res['Result']) && $res['Result'] == 1) {
                $resArr = array();
                if (isset($res[0])) {
                    $resArr = $res;
                } else {
                    $resArr[0] = $res;
                }
                foreach ( $resArr as $k => $v ) {
                    if (isset($v['Result']) && $v['Result'] == 1) {
//                        if (!empty($v['VisitTime'])) {
//                            if (date('Y-m-d', strtotime($v['VisitTime'])) < date('Y-m-d', strtotime('-5 day'))) {
//                                continue;
//                            }
//                        }
                        $unpayinfo = [];
                        if (isset($v['dept_no1'])) {
                            $unpayinfo['hos_code'] = $this->getHosCodeByDeptNo($v['dept_no1']);; //等待his加入
                            if (isset($this->hos_name_map[$unpayinfo['hos_code']])) {
                                $unpayinfo['hos_name'] = $this->hos_name_map[$unpayinfo['hos_code']];
                            }
                        } else if (isset($v['dept_no'])) {
                            $unpayinfo['hos_code'] = $this->getHosCodeByDeptName($v['dept_no']);; //等待his加入
                            if (isset($this->hos_name_map[$unpayinfo['hos_code']])) {
                                $unpayinfo['hos_name'] = $this->hos_name_map[$unpayinfo['hos_code']];
                            }
                        } else {

                            $unpayinfo['hos_code'] = "";
                        }
                        $unpayinfo['jz_card'] = $jz_card;
                        $unpayinfo['patient_id'] = $patient_id;
                        $unpayinfo['patient_name'] = $v['PatientName'];
                        $unpayinfo['bill_id'] = $v['AdmissTimes'];
                        $unpayinfo['dep_name'] = isset($v['VisitDept']) ? $v['VisitDept'] : '';
                        $unpayinfo['doc_name'] = isset($v['VisitDoctor']) ? $v['VisitDoctor'] : '';
                        $unpayinfo['bill_time'] = isset($v['VisitTime']) ? $v['VisitTime'] : '';
                        $unpayinfo['isGT'] = isset($v['GT']) && $v['GT']=='3' ? 1 : 0;
                        $unpayinfo['total_price'] = $v['ChargeTotal'];
    //                     $unpayinfo['info'] = $v;
                        if(!empty($v['BillItems'])){
                            $billList = [];
                            $itemList = explode(';',$v['BillItems']);
                            foreach ($itemList as $key=>$value){
                                $temp =[];
                                $item = explode(':',$value);
                                $temp['name'] = $item[0];
                                $temp['price'] = $item[1];
                                array_push($billList,$temp);
                            }
                            $unpayinfo['bill_list'] = $billList;
                        }
                        $unPayList[] = $unpayinfo;
                    }
                }
//             }
        }
        if ($unPayList) {
            $bill_id = [];
            $returnList = [];
            $nowTimestamp = time();
            foreach($unPayList as $value) {
                if (empty($bill_id[$value['bill_id']])) {
                    $bill_id[$value['bill_id']] = 1;
                } else {
                    system_log('调用his待缴费接口返回多条记录:' .json_encode($res).' 结果：'.json_encode($unPayList));
                    break;
                }
                $itemTime = strtotime($value['bill_time']);
//                if($itemTime + 259200  > $nowTimestamp) {
                    array_push($returnList, $value);
//                }
            }
            return $this->returnSuccess($returnList);
        } else {
            return $this->returnErorr('未查到缴费记录');
        }
    }
    // 获取门诊处方详情信息
    public function getMzCfDetail($options = array()) {
        if (empty($options)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }
        //TODO MI test
        if ($options['jz_card'] == '17339991') {
            $Request['MethodName'] = 'MzChuFangCX2024';
        } else{
            $Request['MethodName'] = 'MzChuFangCX';
        }
        $Request['PatName'] = $options['patient_name'];
//        $Request['PatName'] = '小小天';
        $Request['PatientID'] = $options['jz_card'];
//        $Request['PatientID'] = '17052061';
        $Request['AdmissTimes'] = $options['bill_id'];
//        $Request['AdmissTimes'] = '21';
        $res = $this->call($Request);
        if (isset($res[0])) {
            $resArr = $res;
        } else {
            $resArr[0] = $res;
        }
        $cfList = [];
        foreach ($resArr as $value){
            if (isset($value['Result']) && $value['Result'] == 1) {
                $temp = [];
                $temp['name'] = $value['NAME'];
                $temp['spec'] = $value['Spec'];
                $temp['amount'] = number_format($value['Amount'],2);
                $temp['dosage_amount'] = number_format($value['Dosage_amount'],3);
                $temp['dosage_unit'] = $value['Dosage_unit'];
                $temp['supply_name'] = $value['Supply_name'];
                $temp['freq'] = $value['Freq'];
                $temp['struction'] = !empty($value['Struction'])?$value['Struction']:'';//说明
                $cfList[] = $temp;
            }
        }
//        dump($cfList);die;
        if(!empty($cfList)){
            return $this->returnSuccess($cfList);
        } else {
            return $this->returnErorr('未查到处方信息');
        }
    }
    public function getMzUnPayListbak($options = array()) {
        $unPayList = [];
        $jz_cards = [];
        if (empty($options)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }
        foreach ($options as $value) {
            $jz_cards[$value['jz_card']] = $value['pat_id'];
        }

        if (empty($jz_cards)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }

        foreach ($jz_cards as $jz_card => $patient_id) {
            //TODO MI test
            if ($jz_card == '17339991' || $jz_card == '18936182') {
                $Request['MethodName'] = 'MZ_GetUnPayList2024';
            } else{
                $Request['MethodName'] = 'MZ_GetUnPayList';
            }
            $Request['PatientID'] = $jz_card;
            $res = $this->call($Request);
            if (config('app_debug')) {
                system_log('调用his待缴费接口返回:' .json_encode($res));
            }
//             if (isset($res['Result']) && $res['Result'] == 1) {
                $resArr = array();
                if (isset($res[0])) {
                    $resArr = $res;
                } else {
                    $resArr[0] = $res;
                }
                foreach ( $resArr as $k => $v ) {
                    if (isset($v['Result']) && $v['Result'] == 1) {
                        if (empty($unPayList[$v['AdmissTimes']])) {
                            $unpayinfo = [];
                            if (isset($v['dept_no1'])) {
                                $unpayinfo['hos_code'] = $this->getHosCodeByDeptNo($v['dept_no1']);; //等待his加入
                                if (isset($this->hos_name_map[$unpayinfo['hos_code']])) {
                                    $unpayinfo['hos_name'] = $this->hos_name_map[$unpayinfo['hos_code']];
                                }
                            } else {
                                $unpayinfo['hos_code'] = "";
                            }
                            $unpayinfo['jz_card'] = $jz_card;
                            $unpayinfo['patient_id'] = $patient_id;
                            $unpayinfo['patient_name'] = $v['PatientName'];
                            $unpayinfo['bill_id'] = $v['AdmissTimes'];
                            $unpayinfo['dep_name'] = isset($v['VisitDept']) ? $v['VisitDept'] : '';
                            $unpayinfo['doc_name'] = isset($v['VisitDoctor']) ? $v['VisitDoctor'] : '';
                            $unpayinfo['bill_time'] = $v['VisitTime'];
                            $unpayinfo['total_price'] = floatval(number_format($v['ChargeTotal'], 2 ));
                            $unPayList[$v['AdmissTimes']] = $unpayinfo;
                        } else {
                            $unPayList[$v['AdmissTimes']]['total_price'] += floatval(number_format($v['ChargeTotal'], 2 ));
                        }
                    }
                }
//             }
        }
        if ($unPayList) {
            $bill_id = [];
            foreach ($unPayList as $value) {
                if (empty($bill_id[$value['bill_id']])) {
                    $bill_id[$value['bill_id']] = 1;
                } else {
                    system_log('调用his待缴费接口返回多条记录:' .json_encode($res).' 结果：'.json_encode($unPayList));
                    break;
                }
            }
            return $this->returnSuccess($unPayList);
        } else {
            return $this->returnErorr('未查到缴费记录');
        }
    }
    // 获取门诊已缴费清单
    public function getMzPayList($options = array()) {
        $payedList = [];
        $jz_cards = [];
        if (empty($options)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }
        foreach ($options as $value) {
            $jz_cards[$value['jz_card']] = $value['pat_id'];
        }

        if (empty($jz_cards)) {
            return $this->returnErorr('没有查询到就诊卡信息');
        }

        foreach ($jz_cards as $jz_card => $patient_id) {
            //TODO MI test
            if ($jz_card == '17339991' || $jz_card == '18936182') {
                $Request['MethodName'] = 'MZ_GetPayList2024';
            } else{
                $Request['MethodName'] = 'MZ_GetPayList';
            }
            $Request['PatientID'] = $jz_card;
            $res = $this->call($Request);
            if (isset($res['Result']) && $res['Result'] == 1) {
                $resArr = array();
                if (isset($res[0])) {
                    $resArr = $res;
                } else {
                    $resArr[0] = $res;
                }
                foreach ( $resArr as $k => $v ) {
                    $payedinfo = [];
                    /*
                    if (isset($v['DeptNo'])) {
                        $payinfo['hos_code'] = $this->getHosCodeByDeptNo($v['DeptNo']);; //等待his加入
                        if (isset($this->hos_name_map[$payedinfo['hos_code']])) {
                            $payedinfo['hos_name'] = $this->hos_name_map[$payedinfo['hos_code']];
                        }
                    } else {
                        $payedinfo['hos_code'] = "";
                    }
                    $payedinfo['jz_card'] = $jz_card;
                    $payedinfo['patient_id'] = $patient_id;
                    $payedinfo['patient_name'] = $v['PatientName'];
                    $payedinfo['bill_id'] = $v['AdmissTimes'];
                    $payedinfo['dep_name'] = isset($v['VisitDept']) ? $v['VisitDept'] : '';
                    $payedinfo['doc_name'] = $v['VisitDoctor'];
                    $payedinfo['bill_time'] = $v['VisitTime'];
                    $payedinfo['total_price'] = $v['ChargeTotal'];
                    */

                    $payedinfo['jz_card'] = $jz_card;
                    $payedinfo['pay_date'] = $v['ChargeDate'];
                    $payedinfo['total_price'] = $v['ChargeTotal'];
                    $payedList[] = $payedinfo;
                }
            }
        }
        if ($payedList) {
            return $this->returnSuccess($payedList);
        } else {
            return $this->returnErorr('未查到缴费记录');
        }
    }

    /**
     * 根据科室ID获取院区code
     * @param unknown $deptno
     * @return string
     */
    protected function getHosCodeByDeptNo($deptno) {
        if (empty($deptno)) {
            return "";
        }
        if (isset($this->deptno_hoscode_map[strval($deptno)])) {
            return $this->deptno_hoscode_map[strval($deptno)];
        } else {
            return "";
        }
    }
    protected function getHosCodeByDeptName($deptno) {
        if (empty($deptno)) {
            return "";
        }
        if (isset($this->deptname_hoscode_map[strval($deptno)])) {
            return $this->deptname_hoscode_map[strval($deptno)];
        } else {
            return "";
        }
    }
    // 创建门诊订单
    public function createMzOrder($options = array()) {
        if (empty($options['jz_card'])) {
            return $this->returnErorr('就诊卡不能为空');
        }
        if (empty($options['bill_id'])) {
            return $this->returnErorr('单据号不能为空');
        }
        $order = null;
        // 查询数据库中是否创建过订单,如果已经创建过订单,就没有必要再次创建订单
        $where['user_id'] = $options['userid'];
        $where['pat_id'] = $options['pat_id'];
        if (isset($options['hos_code']) && $options['hos_code']) {
            $where['hos_id'] = $options['hos_code'];
        }
        $where['jz_card'] = $options['jz_card'];//患者ID号
        $where['bill_id'] = $options['bill_id'];//就诊次数
        // 根据指定的患者ID号和就诊次数查询订单表
        $order = Db::name('mz_pay_order')->where($where)->order('create_time desc')->find();
        // 根据指定的患者ID号查询待缴费记录
        $result = $this->getMzUnPayList([$options]);
        if ($result['code'] == 1) {
            foreach ( $result['data'] as $k => $v ) {
                if ($v['bill_id'] == explode("$", $options['bill_id'])[0]) {
                    // 如果就诊次数一致则处理订单
                    if ($order) {
                        try {
                            system_log("已有订单记录：".json_encode($order).' 返回待缴费结果：'.json_encode($result));
                        } catch (\Exception $e) {
                        }
                    }
                    if ($order && $order['status'] ==0 && $order['total_price'] == $v['total_price'] && !$options['is_mi_order']) {
                        // 如果相同的患者ID和就诊次数已经存在价格一样的未知付订单,跳出循环,返回已创建的订单进行支付
                        // 价格不一样未支付则新建订单（防止处方修改）
                        try {
                            Db::name('MzPayOrder')->update(['create_time'=>time(),'update_time'=>time(),'id'=>$order['id']]);
                        } catch (\Exception $e) {
                        }
                        break;
                    }
                    /*
                    if ($order && $order['status'] == 1 && $order['total_price'] == $v['total_price']) {
                        // 患者ID一致、就诊次数一致、且价格一致，已支付的订单无需再次支付
                        system_log('该项目已经缴费了，请到医院确认');
                        return $this->returnErorr('该项目已经缴费了，请到医院确认');
                    }
                    */
                    // 其他情况则新建订单（第一次支付，或是支付失败，或是金额有变动获取最新的金额创建订单支付）
                    // 创建订单
                    $order = array();
                    $orderNo = $this->genOrderNo($prefix = 'MZ');
//                     $patient = model('Patient')->getDetail($options['pat_id']);
//                     $order['user_id'] = $patient['user_id'];

                    $order['user_id'] = $options['userid'];
                    $order['pat_id'] = $options['pat_id'];
                    $order['order_no'] = $orderNo;
                    $order['hos_id'] = $v['hos_code'];
                    if (isset($v['hos_name']) && $v['hos_name']) {
                        $order['hos_name'] = $v['hos_name'];
                    } else {
                        $hospital_info = $this->getHospitalDetail($v['hos_code']);
                        if ($hospital_info) {
                            $order['hos_name'] = $hospital_info['hospital_name'];
                        }
                    }
                    $order['jz_card'] = $v['jz_card'];
                    $order['patient_name'] = $v['patient_name'];
                    $order['bill_id'] = $v['bill_id'];
                    $order['dep_name'] = $v['dep_name'];
                    $order['doc_name'] = $v['doc_name'];
                    $order['bill_time'] = $v['bill_time'];
                    $order['total_price'] = $v['total_price'];
                    $order['is_mi_order'] = $options['is_mi_order'];
                    $order['create_time'] = time();
                    $order['update_time'] = time();
                    // 查询该次处方有没有在其他人手机上交过费--start
                    $existsOrderNo = null;
                    $where['jz_card'] = $order['jz_card'];//患者ID号
                    $where['bill_id'] = $order['bill_id'];//就诊次数
                    $where['total_price'] = $order['total_price'];//价格
                    $where['user_id'] = ['<>',$order['user_id']];//
                    $where['status'] = ['>=',0];//待支付或者支付成功
                    // 根据指定的患者ID号和就诊次数查询订单表
                    $existsOrderNo = Db::name('mz_pay_order')->where($where)->field('order_no')->find();
                    if($existsOrderNo){
                        system_log("该处方已在其他手机上产生订单(订单号:$existsOrderNo),重复缴费容易导致缴费失败,就诊卡号:".$order['jz_card']);
                        return $this->returnErorr("该处方已在其他手机上产生订单(订单号:$existsOrderNo),重复缴费容易导致缴费失败", 0);
                    }
                    //查询该次处方有没有在其他人手机上交过费--end
                    $id = Db::name('MzPayOrder')->insertGetId($order);
                    $order['info'] = $v;
                    $order['id'] = $id;
                    //保存缴费费用类别清单
                    if(!empty($v['bill_list'])){
                        $cfList=[];
                        foreach ($v['bill_list'] as $billV){
                            $temp=[];
                            $temp['name'] = $billV['name'];
                            $temp['num'] = 1;
                            $temp['price'] = $billV['price'];
                            $temp['order_id'] = $id;
                            $cfList[] = $temp;
                        }
                        $result =Db::name('mz_pay_list')->insertAll($cfList);
                    }
                    break;
                }
            }
            if ($order) {
                $order['item_list'] = null; // [['name'=>"挂号费",'price'=>7.50],['name'=>"抽血化验",'price'=>200.00]];//缴费项目清单,儿童医院默认为空
                return $this->returnSuccess($order);
            } else {
                return $this->returnErorr('没有查询到待缴费记录，请联系收费处查询');
            }
        } else {
            return $this->returnErorr('没有查询到待缴费记录，请联系收费处查询');
        }
    }

    /**
     * @throws \Exception
     */
    public function createMzMiPayOrder($mzOrder = array(), $miAuthInfo = array(), $channel = "WECHAT", $is_rels_pay = 0) {
        $payAuthNo = $miAuthInfo['pay_auth_no'];
        $longitude = $miAuthInfo['user_longitude_latitude']['longitude'];
        $latitude = $miAuthInfo['user_longitude_latitude']['latitude'];

//        $request['MethodName'] = 'Mz_HuaJia';
        $request['FunctionName'] = 'Mz_HuaJia';
        $request['PatientID'] = "00".$mzOrder['jz_card']."00";
        $request['AdmissTimes'] = $mzOrder['bill_id'];
        $request['PayAuthNo'] = $payAuthNo;
        $request['uldLatlnt'] = $longitude.",".$latitude;
//        $request['FunctionName'] = "MZ_GetVisitDiseinfo";
        system_log('开始调用昆明市儿童医院his接口进行医保划价:' . json_encode($request));
        $res = $this->callMi($request);
        system_log("开始调用his医保划价接口, 参数：" . json_encode($request).' 返回：'.json_encode($res));
        if (!isset($res['Result']) || $res['Result'] != 0) {
            system_log("调用his医保划价接口失败:".json_encode($res));
            throw new \Exception("调用his医保划价接口失败");
        }
        $miOrder = array();
        $miOrder['channel'] = $channel;
        $miOrder['order_type'] = 2;
        $miOrder['order_no'] = $mzOrder['order_no'];
        $miOrder['his_order_no'] = $res['serialNo'];
        $miOrder['org_no'] = 'H53011200038';
        $miOrder['gmt_out_create'] = date("YmdHis" ,strtotime($res['gmtOutCreate']));
        $miOrder['pay_ord_id'] = $res['payOrdId'];
        $miOrder['pay_auth_no'] = $payAuthNo;
        $miOrder['setl_lat_int'] = $longitude.",".$latitude;
        $miOrder['ord_stas'] = $res['ordStas'];
        $miOrder['total_fee'] = $res['feeSumamt'] - $res['cash_reduced_fee'];
        $miOrder['fee_sumamt'] = $res['feeSumamt'];
        $miOrder['own_pay_amt'] = $res['ownPayAmt'];
        $miOrder['psn_acct_pay'] = $res['psnAcctPay'];
        $miOrder['fund_pay'] = $res['fundPay'];
        $miOrder['deposit'] = $res['deposit'];
        $miOrder['cash_add_fee'] = 0;
        $miOrder['cash_add_wording'] = '';
        $miOrder['cash_reduced_fee'] = $res['cash_reduced_fee'];
        $miOrder['cash_reduced_wording'] = "院内减免";
        $miOrder['created_date'] = date('Y-m-d H:i:s', time());
        $miOrder['last_modified_date'] = date('Y-m-d H:i:s', time());
        $miOrder['order_status'] = 0;
        $miOrder['is_rels_pay'] = $is_rels_pay;
        if ($channel == "Alipay") {
            $miOrder['medical_card_id'] = $miAuthInfo['medical_card_id'];
            $miOrder['medical_card_inst_id'] = $miAuthInfo['medical_card_inst_id'];
        }
        /*if ($res['fundPay'] == 0 && $res['psnAcctPay'] == 0 && $res['ownPayAmt'] > 0) {
            throw new \Exception("当前订单无法使用医保支付，请选择其他支付方式");
        }*/
        $id = Db::name('MiOrder')->insertGetId($miOrder);
        $miOrder['id'] = $id;
        $miOrder['cash_fee'] = $miOrder['own_pay_amt'] - $miOrder['cash_reduced_fee'];
        return $miOrder;
    }

    /*
     * 微信支付成功后调用医院支付接口
     *
     */
    public function payMzOrder($options = array()) {
        $payOrderModel = new MzPayOrder();
        $payOrder = $payOrderModel->getMzOrderPayInfo($options['order_id']);
        $orderNo = $payOrder['order_no'];
        $have_unpaylist = false;
        $needCheckUnpaylist = false;
        $res = [];
        if ($needCheckUnpaylist) {
            try {
                system_log("----------".date('Y-m-d H:i:s')."开始处理门诊订单[$orderNo]支付-------------");
                system_log(json_encode($payOrder));
                // 根据指定的患者ID号查询待缴费记录
                $result = $this->getMzUnPayList([['jz_card'=>$payOrder['jz_card'], 'pat_id'=>$payOrder['pat_id']]]);
                if ($result['code'] == 1) {
                    foreach ( $result['data'] as $k => $v ) {
                        if ($v['bill_id'] == $payOrder['bill_id']) {
                            $have_unpaylist = true;
                            break;
                        }
                    }
                }
            } catch (\Exception $e) {
                system_log('payMzOrder 出错：'.$e->getFile().$e->getLine().$e->getMessage());
            }
        } else {
            $have_unpaylist = true;
        }
        
        if ($have_unpaylist) {
            //TODO MI test
            if ($payOrder['jz_card'] == '17339991') {
                $Request['MethodName'] = 'MZ_Payment2024';
            } else{
                $Request['MethodName'] = 'MZ_Payment';
            }
            $Request['PatientID'] = $payOrder['jz_card'];
            $Request['AdmissTimes'] = $payOrder['bill_id'];
            $Request['PayID'] = $payOrder['bill_id'];
            //         $Request['PayType'] = 'f'; // 与医院人员确认支付类型
            $Request['PayType'] = 'd'; // 与医院人员确认支付类型
            $Request['TransactionID'] = $payOrder['transaction_id'];
            $Request['OutTradeNo'] = $payOrder['transaction_id'];
            $Request['ChargeTotal'] = $payOrder['total_price'];
            // dump($Request);die;
            system_log('开始调用昆明市儿童医院his接口,支付订单:' . json_encode($Request));
            $res = $this->call($Request);
            // $res['Result']=1;
            system_log("开始调用his门诊支付接口, 参数：" . json_encode($Request).' 返回：'.json_encode($res));
            
            if (isset($res['Result']) && $res['Result'] == 1) {
                // 修改数据库状态
                $resMsg = isset($res['Error']) ? $res['Error'] : "支付成功";
                $queue_sn = isset($res['Queue_sn']) ? $res['Queue_sn'] : 0; // 排队序号
                $data['id'] = $payOrder['id'];
                $data['queue_sn'] = $queue_sn;
                $data['res_msg'] = $resMsg;
                try {
                    $data['update_time'] = !empty($payOrder['pay_time'])?$payOrder['pay_time']:time();
                } catch (\Exception $e) {
                    $data['update_time'] = time();
                }
                
                $data['status'] = 1; // 支付成功
                system_log(date("Y-m-d H:i:s")." 调用his接口支付成功,开始更新数据库状态");
                $updateResult = $payOrderModel->updateMzRecordOrder($data);


                // 将支付信息再次通知默联
                try {

                    $RequestMl['OrderNo'] = $payOrder['his_order_no'];
                    $RequestMl['Hislsh'] = $payOrder['transaction_id'];
                    $DataTableMl['data'] = $RequestMl;
                    $xml = new A2Xml();
                    $paramsMl['xml'] = $xml->toXml($DataTableMl);
                    // $data['funCode'] = 'F000120';
                    $paramsMl['funCode'] = 'UpdateOrderInfoForLSH';
                    system_log("儿童门诊缴费,通知默联请求:".json_encode($paramsMl));
//            dump($params);die;
                    $resMl = $this->callMlPost($paramsMl);
                    system_log("儿童门诊缴费,通知默联结果:".json_encode($resMl));

                } catch (\Exception $e) {
                    system_log("默联通知出现异常".$e->getMessage());
                }

                if ($updateResult) {
                    // 发送微信模板消息
                    $openid = $payOrder['openid'];
                    $tepParam = [];
                    $tepParam['first'] = '您好，您已缴费成功。';
                    //缴费项目
                    $tepParam['keyword1'] = $payOrder['dep_name'] . ' ' . $payOrder['doc_name'] . '-门诊费'; // 根据每个医院不同的情况组织缴费项目
                    //金额
                    $tepParam['keyword2'] = $payOrder['total_price'];
                    //就诊人
                    $tepParam['keyword3'] = $payOrder['patient_name'];
                    $tepParam['remark'] = $resMsg?$resMsg:'。感谢您的使用.';
                    $url = config('web_domain') . "/html/mz/paydetail.html?id={$options['order_id']}&type=1";
                    try {
                        $app = new Application(config('wechat'));
                        $notice = $app->notice;
                        $messageId = $notice->send([
                            'touser' => $openid,
                            'template_id' => '6pafr-UUVvG17l4Z73Z-4T9Q1V-vXoEzhQ8O2ISNNbg',
                            'url' => $url,
                            'data' => $tepParam,
                        ]);
                    } catch (\Exception $e) {
                        system_log('发送微信模板消息失败：门诊缴费成功，to_openid:'.$openid);
                    }
                    system_log("-------------支付成功,订单号[$orderNo]----------------");

//                    Wenjuan::sendWxTemplate($openid,$payOrder);//满意度调查表
                    return self::returnSuccess();
                } else {
                    system_log("支付失败,更新数据库发生错误");
                    $res['Error'] = "支付失败,更新数据库发生错误";
                    //return self::returnErorr('支付失败,请联系医院工作人员');
                }
            }
        }
        if (!$have_unpaylist) {
            $errMsg = '未查询到待缴费记录';
            $errInfo = '调his支付失败!订单号：[' . $orderNo . '],' . $errMsg;
        } else {
            $errMsg = isset($res['Error']) ? $res['Error'] : ''; // 错误消息
            $errInfo = '调his支付失败!订单号：[' . $orderNo . '],his接口返回:' . $errMsg;
        }
        system_log($errInfo);
        if ($errMsg != '此流水号已存在') {
            $payOrderInfo = $payOrderModel->getMzOrderPayInfo($options['order_id']);
            if ($payOrderInfo['status'] != 1) {
                // 当前订单没有支付成功
                // 记录his调用失败的
                $data['id'] = $payOrder['id'];
                $data['status'] = - 1; // -1:微信支付成功,his接口支付失败;1:支付成功
                $data['res_msg'] = $errMsg;
                
                system_log(date("Y-m-d H:i:s")." 调用his接口支付失败,开始更新数据库状态");
                $updateResult = $payOrderModel->updateMzRecordOrder($data);
            }
        }
        
        // 记录错误日志
        $errData['order_no'] = $orderNo;
        $errData['order_event'] = self::MZ_TRADE_TYPE_APY;
        $errData['event_info'] = $errInfo;
        model('ExceptionLog')->data($errData)->save();
        // 给开发者发送信息
        $sendWechatData = ['due' => $errInfo,'time' => date('Y-m-d H:i:s')];
        
        $msg = "系统故障原因报告：门诊缴费接口错误：{$errInfo}\n时间：".date('Y-m-d H:i:s');
        
        $tepParam = [];
        $tepParam['first'] = '门诊缴费失败原因报告：';
        //缴费项目
        $tepParam['keyword1'] = $payOrder['dep_name'] . ' ' . $payOrder['doc_name'] . '-门诊费'; // 根据每个医院不同的情况组织缴费项目
        //金额
        $tepParam['keyword2'] = $payOrder['total_price'];
        //就诊人
        $tepParam['keyword3'] = $payOrder['patient_name'];
        $tepParam['remark'] = $msg;
        try {
            if ($errMsg != '此流水号已存在') {
                $app = new Application(config('wechat'));
                $notice = $app->notice;
                $messageId = $notice->send([
                    'touser' => self::$DEV_OPENID,
                    'template_id' => 'xKhV9x1J0UudrF3J_0goZPMxOZy61GVTBOX7w4_3mf8',
                    'data' => $tepParam,
                ]);
            }
        } catch (\Exception $e) {
            system_log('发送微信模板消息失败：门诊缴费失败，to_openid:'.self::$DEV_OPENID);
        }
        
        return $this->returnErorr($this->replaceMsg($errMsg));
    }
    
    // 获取分诊排队信息
    public function waitList($options = array()) {
        // 获取病人的就诊卡
        if (empty($options)) {
            return $this->returnErorr('就诊卡信息不能为空');
        }
        /*
         * $k=0;
         * $data[$k]['patient_card'] = '123123';//就诊卡（病历号）
         * $data[$k]['patient_name'] = '就诊人测试';//就诊人姓名
         * $data[$k]['depart_name'] = 'xxx科室';//科室名
         * $data[$k]['doctor_name'] = '牛大夫';//医生名
         * $data[$k]['see_doctor_date'] = date('Y-m-d');//看诊日期
         * $data[$k]['see_doctor_time_type'] = '上午';//看诊时间类型
         * $data[$k]['my_see_no'] = '25';//排队编号
         * $data[$k]['now_see_num'] = '5';//前面候诊排队人数
         * $data[$k]['wait_time'] = '30';//前面候诊排队人数
         * return $this->returnSuccess($data);
         */
        $Request['MethodName'] = 'BK_GetDistInfo';
        $Request['PatientID'] = $options['jz_card'];
        $res = $this->call($Request);
        $data = null;
        if (isset($res['Result']) && $res['Result'] == 1) {
            $resArr = array();
            if (isset($res[0])) {
                $resArr = $res;
            } else {
                $resArr[0] = $res;
            }
            foreach ( $resArr as $k => $v ) {
                $data[$k]['jz_card'] = $options['jz_card']; // 就诊卡（病历号）
                $data[$k]['patient_name'] = $options['patient_name'];//就诊人姓名
                $data[$k]['depart_name'] = isset($v['VisitDeptName']) ? $v['VisitDeptName'] : ''; // 科室名
                $data[$k]['doctor_name'] = isset($v['VisitDoctName'])?$v['VisitDoctName']:''; // 医生名
                $data[$k]['see_doctor_date'] = isset($v['VisitDate']) ? $v['VisitDate'] : date('Y-m-d'); // 看诊日期
                $data[$k]['see_doctor_time_type'] = $this->timeTypeToStr($v['Shift']); // 看诊时间类型
                $data[$k]['my_see_no'] = $v['QueueSN']; // 排队编号
                $data[$k]['now_see_num'] = isset($v['PatWaitCount'])?$v['PatWaitCount']:0; // 前面候诊排队人数
                $data[$k]['wait_time'] = isset($v['WaitTime'])?$v['WaitTime']:0; // 预计等待时间(单位分钟)
                
                $data[$k]['now_see_no'] = ''; // 当前编号
                $data[$k]['appoint_no'] = ''; // 挂号流水号(支付成功返回的)
                $data[$k]['doctor_level'] = ''; // 医生级别
            }
            return $this->returnSuccess($data);
        } else if (isset($res['Error']) && $res['Error']) {
            return $this->returnErorr($res['Error']);
        } else if (isset($res['Result']) && $res['Result'] == 0) {
            return $this->returnErorr('未获取到排队信息，如已经分诊请刷新页面重试');
        } else if (is_array($res)) {
            $resArr = array();
            if (isset($res[0])) {
                $resArr = $res;
            } else {
                $resArr[0] = $res;
            }
            try {
                
                foreach ( $resArr as $k => $v ) {
                    $data[$k]['jz_card'] = $options['jz_card']; // 就诊卡（病历号）
                    $data[$k]['patient_name'] = $options['patient_name'];//就诊人姓名
                    $data[$k]['depart_name'] = isset($v['VisitDeptName']) ? $v['VisitDeptName'] : ''; // 科室名
                    $data[$k]['doctor_name'] = isset($v['VisitDoctName'])?$v['VisitDoctName']:''; // 医生名
                    $data[$k]['see_doctor_date'] = isset($v['VisitDate']) ? $v['VisitDate'] : date('Y-m-d'); // 看诊日期
                    $data[$k]['see_doctor_time_type'] = $this->timeTypeToStr($v['Shift']); // 看诊时间类型
                    $data[$k]['my_see_no'] = $v['QueueSN']; // 排队编号
                    //                 $data[$k]['now_see_num'] = $v['PatWaitCount']; // 前面候诊排队人数
                    //                 $data[$k]['wait_time'] = $v['WaitTime']; // 预计等待时间(单位分钟)
                    $data[$k]['now_see_num'] = isset($v['PatWaitCount'])?$v['PatWaitCount']:0; // 前面候诊排队人数
                    $data[$k]['wait_time'] = isset($v['WaitTime'])?$v['WaitTime']:0; // 预计等待时间(单位分钟)
                    
                    $data[$k]['now_see_no'] = ''; // 当前编号
                    $data[$k]['appoint_no'] = ''; // 挂号流水号(支付成功返回的)
                    $data[$k]['doctor_level'] = ''; // 医生级别
                }
            } catch (\Exception $e) {
                system_log('BK_GetDistInfo返回:'.json_encode($res));
            }
            return $this->returnSuccess($data);
        } else {
            return $this->returnErorr('未获取到排队信息');
        }
    }
    
    /**
     * 医生停诊信息
     * 
     * @param array $options
     *            参数
     * @param string $options[hos_code]
     *            医院code
     */
    function getStopwork($options = array()) {
        return $this->returnErorr("目前还未开通");
    }
    
    /**
     * 医生停诊专用通道--退号退费发送模板消息发送短信通知
     * 
     * @param array $options
     *            参数
     * @param string $options[hos_code]
     *            医院code
     */
    function stopworkTimer($options = array()) {
        return $this->returnErorr("目前还未开通");
    }
    /**
     * 预约就诊签到
     * 
     * @param array $options
     *            参数
     * @param string $options[hos_code]
     *            医院code
     * @param string $options[order_id]
     *            预约ID
     */
    function appointSign($options = array()) {
        return $this->returnErorr("目前还未开通");
    }
    
    /**
     * 获取就诊卡余额
     * 
     * @param
     *            string jzCard 患者ID号
     */
    public function getCardBalance($jzCard = '') {
        return $this->returnErorr("目前还未开通");
    }
    
    /**
     * 选择一个就诊人
     * 
     * @param string $options 就诊人信息
     */
    public function selectPatient($options = []) {
        $selectedPatient = null;
        $patientInfo = isset($options['patientInfo'])?$options['patientInfo']:null;
        $patientData = isset($options['patientData'])?$options['patientData']:null;
        if (empty($patientInfo) || empty($patientData)) {
            return $selectedPatient;
        }
        $sameList = []; //姓名，电话，生日相同的就诊人列表
        foreach ($patientData as $patient) {
            if ($patient['patient_phone'] == $patientInfo['patient_phone'] && $patient['patient_birthday'] == $patientInfo['patient_birthday'] && $patient['patient_name'] == $patientInfo['patient_name']) {
                $sameList[] = $patient;
            }
        }
        if ($sameList) {
            $allSameList = [];
            foreach ($sameList as $samePatient) {
                if ($patient['guarantee_name'] == $patientInfo['guarantee_name'] && $patient['patient_relation'] == $patientInfo['patient_relation'] && $patient['patient_sex'] == $patientInfo['patient_sex']) {
                    // 监护人关系和监护人姓名、性别一致
                    $allSameList[] = $patient;
                }
            }
            if ($allSameList) {
                $selectedPatient = array_shift($allSameList);
                return $selectedPatient;
            }
            
            foreach ($sameList as $samePatient) {
                if ($patient['guarantee_name'] == $patientInfo['guarantee_name'] && $patient['patient_relation'] == $patientInfo['patient_relation']) {
                    // 监护人关系和监护人姓名一致
                    $allSameList[] = $patient;
                }
            }
            if ($allSameList) {
                $selectedPatient = array_shift($allSameList);
                return $selectedPatient;
            }
            
            foreach ($sameList as $samePatient) {
                if ($patient['guarantee_name'] == $patientInfo['guarantee_name']) {
                    // 监护人关系和监护人姓名一致
                    $allSameList[] = $patient;
                }
            }
            if ($allSameList) {
                $selectedPatient = array_shift($allSameList);
                return $selectedPatient;
            }
            
            $selectedPatient = array_shift($sameList);
            return $selectedPatient;
        }
        return $selectedPatient;
    }
    
    public function getCheckList($options = array()) {
        $Request['hisRegNo'] = $options['jz_card'];
        $res = $this->callPacs('searchPatientList', $Request);
        //$res = $this->getCheckListDemo($options['jz_card']);
        $data = [];
        if (isset($res['ErrorCode']) && $res['ErrorCode'] == 0) {
//             system_log('查询检查报告列表返回：'.json_encode($res));
            $resArr = [];
            if (isset($res['Content'][0])) {
                $resArr = $res['Content'];
            } else {
                $resArr[0] = $res['Content'];
            }
            foreach ( $resArr as $k => $v ) {
                $data[$k] = $options;                           // merge就诊人信息
                $data[$k]['jz_card'] = $options['jz_card'];     // 就诊卡（病历号）
                $data[$k]['report_name'] = !empty($v['ApplyName'])?$v['ApplyName']:'检查报告单';    // 报告单名称
                $data[$k]['report_date'] = $v['ReportDate']?$v['ReportDate']:'';      // 开单时间
                $data[$k]['report_id'] = $v['HisUid'];  //报告ID
                $data[$k]['patient_name'] = $v['PatientName'];  // 病人
            }
            return $this->returnSuccess($data);
        } else {
            return $this->returnErorr($res['ErrorMsg']);
        }
        
    }
    
    public function getCheckDetail($options = array()) {
        $Request['hisUid'] = $options['report_id'];
        $res = $this->callPacs('searchReportInfo', $Request);
        //$res = $this->getCheckDetailDemo($options['report_id']);
//         system_log('查询检查报告详细返回：'.json_encode($res));
        $data = [];
        if (isset($res['ErrorCode']) && $res['ErrorCode'] == 0) {
            $data = $options;                           // merge就诊人信息
            $data['jz_card'] = $options['jz_card'];     // 就诊卡（病历号）
            $data['report_name'] = isset($res['Content']['ApplyName'])?$res['Content']['ApplyName']:'检查报告单';    // 报告单名称
            $data['report_id'] = $options['report_id'];  // 开单医生
            $data['patient_name'] = isset($res['Content']['PatientName'])?$res['Content']['PatientName']:'';  // 开单医生
            $data['patient_age'] = isset($res['Content']['PatientAge'])?$res['Content']['PatientAge']:'';  // 开单医生
            $data['patient_sex'] = isset($res['Content']['PatientSex'])?$res['Content']['PatientSex']:'';  // 开单医生
            $data['report_dept'] = isset($res['Content']['ApplyDept'])?$res['Content']['ApplyDept']:'';  // 申请科室名称
            $data['report_clinic_diag'] = isset($res['Content']['ClinicDiag'])?$res['Content']['ClinicDiag']:'';  // 临床诊断
            $data['report_body'] = isset($res['Content']['BodyOfCase'])?$res['Content']['BodyOfCase']:'';  // 检查部位
            $data['report_desc'] = isset($res['Content']['ReportDesc'])?$res['Content']['ReportDesc']:'';  // 检查所见
            $data['report_diag'] = isset($res['Content']['ReportDiag'])?$res['Content']['ReportDiag']:'';  // 诊断结果
            $data['report_date'] = isset($res['Content']['AuditDate'])?$res['Content']['AuditDate']:'';  // 检查日期报告审核时间
            return $this->returnSuccess($data);
        } else {
            return $this->returnErorr($res['ErrorMsg']);
        }
        
    }
    
    /**
     * 检验报告列表
     * {@inheritDoc}
     * @see \app\common\service\HisAdapter::getProveList()
     */
    public function getProveList($options = array()) {
        $Request['MethodName'] = 'LIS_GetReportList';
        $Request['PatientID'] = $options['jz_card'];
        $res = $this->call($Request);
        $data = [];
        /*
        if (isset($res['Result']) && $res['Result'] == 1) {
            $resArr = array();
            if (isset($res[0])) {
                $resArr = $res;
            } else {
                $resArr[0] = $res;
            }
            foreach ( $resArr as $k => $v ) {
                $data[$k] = $options;                           // merge就诊人信息
                $data[$k]['jz_card'] = $options['jz_card'];     // 就诊卡（病历号）
                $data[$k]['report_name'] = isset($v['ReportName'])?$v['ReportName']:'';    // 报告单名称
                $data[$k]['apply_date'] = $v['ApplyDate'];      // 开单时间
                $data[$k]['apply_doctor'] = $v['ApplyDoctor'];  // 开单医生
                $data[$k]['apply_doctor_name'] = isset($v['ApplyDoctorName'])?$v['ApplyDoctorName']:'';  // 开单医生
                $data[$k]['report_status'] = $v['ReportStatus'];// 报告单状态
                $data[$k]['print_addr'] = $v['PrintAddr'];      // 打印报告单地点
            }
            return $this->returnSuccess($data);
        } else if (isset($res['Error']) && $res['Error']) {
            return $this->returnErorr($res['Error']);
        } else if (is_array($res)) {
            $resArr = array();
            if (isset($res[0])) {
                $resArr = $res;
            } else {
                $resArr[0] = $res;
            }
            foreach ( $resArr as $k => $v ) {
                $data[$k] = $options;                           // merge就诊人信息
                $data[$k]['jz_card'] = $options['jz_card'];     // 就诊卡（病历号）
                $data[$k]['report_name'] = isset($v['ReportName'])?$v['ReportName']:'';    // 报告单名称
                $data[$k]['apply_date'] = $v['ApplyDate'];      // 开单时间
                $data[$k]['apply_doctor'] = $v['ApplyDoctor'];  // 开单医生
                $data[$k]['apply_doctor_name'] = isset($v['ApplyDoctorName'])?$v['ApplyDoctorName']:'';  // 开单医生
                $data[$k]['report_status'] = $v['ReportStatus'];// 报告单状态
                $data[$k]['print_addr'] = $v['PrintAddr'];      // 打印报告单地点
            }
            return $this->returnSuccess($data);
        } else {
            return $this->returnErorr('未获取到排队信息');
        }
        */
        $resArr = array();
        if (isset($res[0])) {
            $resArr = $res;
        } else {
            $resArr[0] = $res;
        }
        if ($resArr) {
            foreach ( $resArr as $k => $v ) {
                if ($v['Result'] == 0) {
                    continue;
                }
                $data[$k] = $options;                           // merge就诊人信息
                $data[$k]['jz_card'] = $options['jz_card'];     // 就诊卡（病历号）
                $data[$k]['report_name'] = isset($v['ReportName'])?$v['ReportName']:'';    // 报告单名称
                $data[$k]['apply_date'] = $v['ApplyDate'];      // 开单时间
                $data[$k]['apply_doctor'] = $v['ApplyDoctor'];  // 开单医生
                $data[$k]['apply_doctor_name'] = isset($v['ApplyDoctorName'])?$v['ApplyDoctorName']:'';  // 开单医生
                $data[$k]['report_status'] = $v['ReportStatus'];// 报告单状态
                $data[$k]['print_addr'] = $v['PrintAddr'];      // 打印报告单地点
            }
        }
        return $this->returnSuccess($data);
    }
    
    /**
     * 检验报告详细
     * {@inheritDoc}
     * @see \app\common\service\HisAdapter::getProveDetail()
     */
    public function getProveDetail($options = array()) {
        $Request['MethodName'] = 'LIS_GetReportDetail';
        $Request['PatientID'] = $options['jz_card'];
        $Request['ReportID'] = $options['report_id'];
        $res = $this->call($Request);
        if (isset($res['Result']) && $res['Result'] == 1) {
            $data = $options;                           // merge就诊人信息
            $data['jz_card'] = $options['jz_card'];     // 就诊卡（病历号）
            $data['report_name'] = isset($res['ReportName'])?$res['ReportName']:'';    // 报告单名称
            $data['apply_date'] = $res['ApplyDate'];      // 开单时间
            $data['apply_doctor'] = $res['ApplyDoctor'];  // 开单医生
            $data['apply_doctor_name'] = isset($res['ApplyDoctorName'])?$res['ApplyDoctorName']:'';  // 开单医生
            $data['report_status'] = $res['ReportStatus'];// 报告单状态
            $data['print_addr'] = $res['PrintAddr'];      // 打印报告单地点
        } else {
            return $this->returnErorr($res['Error']);
        }
    }
    /**
     * 检验报告列表
     * {@inheritDoc}
     * @see \app\common\service\HisAdapter::getProveList()
     */
    public function getProveListNew($options = array(),$datetype='week') {
        $Request['patInNo'] = $options['jz_card'];
//        dump($options);die;
        $days = 7;//week 默认7天
        if($datetype=='month'){
            $days = 30;
        }elseif($datetype=='month3'){
            $days = 90;
        }
        $Request['dateTimeFrom'] = date("Y-m-d H:i:s", strtotime("-$days day"));
        $Request['dateTimeTo'] = date("Y-m-d H:i:s");
        $Request['patOriId'] = 107;
        // 获取门诊列表
        $resMz = $this->callLis('GetPatientsInfo', $Request);
//        dump($resMz);die;
        $Request['patOriId'] = 108;
        // 获取住院号 有可能是多个住院号
        $res = $this->getZyList($options);
        $data = isset($res['data']) ? $res['data'] : [];
//        dump($data);die;

        $resZy = [];
        if ($data) {
            foreach ($data as $value) {
                if (isset($value['InpatientNO'])) {
                    $Request['patInNo'] = $value['InpatientNO'];
                    $zyReport = $this->callLis('GetPatientsInfo', $Request);
                    if($zyReport) {
                        $resZy += $zyReport;
                    }
                }
            }
        }
//        dump($resZy);die;
        if (($resMz && isset($resMz['PatInfo'])) || ($resZy && isset($resZy['PatInfo']))) {
            $resArr = array();
            if ($resMz && isset($resMz['PatInfo'])) {
                if (isset($resMz['PatInfo'][0])) {
                    $resArr = array_merge($resArr,$resMz['PatInfo']);
                } else {
                    array_push($resArr, $resMz['PatInfo']);
                }
            }
            if ($resZy && isset($resZy['PatInfo'])) {
                if (isset($resZy['PatInfo'][0])) {
//                    $resArr += $resZy['PatInfo'];
                    $resArr = array_merge($resArr,$resZy['PatInfo']);
                } else {
                    $tmp[0] =  $resZy['PatInfo'];
                    $resArr = array_merge($resArr,$tmp);
//                    $resArr[0] = $resZy['PatInfo'];
                }
            }
//            dump($resArr);die;
            return $this->returnSuccess($resArr);
        } else {
            return $this->returnSuccess([]);
        }
    }
    
    /**
     * 检验报告详细
     * {@inheritDoc}
     * @see \app\common\service\HisAdapter::getProveDetail()
     */
    public function getProveDetailNew($options = array()) {
        $Request['patId'] = $options['report_id'];
        $res = $this->callLis('GetResultInfo', $Request);
        if ($res && isset($res['ResultInfo']) && $res['ResultInfo']) {
            if (isset($res['ResultInfo'])) {
                $resArr = array();
                if (isset($res['ResultInfo'][0])) {
                    $resArr = $res['ResultInfo'];
                } else {
                    $resArr[0] = $res['ResultInfo'];
                }
                return $this->returnSuccess($resArr);
            }
        } else {
            return $this->returnSuccess([]);
        }
    }
    
    
    
    
    //住院记录查询
    public function getZyList($options=array()){
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        $Request['MethodName'] = 'ZyHospitalRecords';
        $Request['PatientID'] = $options['jz_card'];
//        $Request['PatientID'] = "15922063";
//        $Request['PatientID'] = '000456369300';
//         $Request['outpatient_no'] = $options['jz_card'];
//         $Request['AdmissTimes'] = 0;
//        dump($Request);
        $res = $this->call($Request);
//        dump($res);die;
        system_log('调用ZY_GetPatChargeInfo 参数'.json_encode($Request).' 接口返回'. json_encode($res));
        $zyList = array();
        if (isset($res['Result']) && $res['Result'] == 0) {
            return $this->returnSuccess([]);
        } else {
            if(isset($res[0])){
                $zyList = $res;
            }else{
                $zyList[0] = $res;
            }
            return $this->returnSuccess($zyList);
        }
    }
    
    //住院费用清单查询
    public function getZyFee($options=array()){
        $zyinfo = [];
        $zyinfo['patient_id'] = $options['patient_id'];
        $zyinfo['patient_name'] = $options['patient_name'];
        $zyinfo['jz_card'] = $options['jz_card'];
        $zyinfo['hos_id'] = '871002';
        $zyinfo['hos_name'] = '前兴院区';
        $zyinfo['dep_name'] = 'XX科室';
        $zyinfo['in_date'] = '2017-10-25';
        $zyinfo['total_fee'] = 1525;
        $zyinfo['balance'] = 23555;
        $zyinfo['zy_num'] = $options['patient_id']+1;
        $zyinfo['status'] = 1;
        
        $fee_list = [];
        $fee_list['2017-10-20'] = [['item_name'=>'XXX','fee'=>123],['item_name'=>'XXX','fee'=>235],['item_name'=>'XXX','fee'=>145]];
        $fee_list['2017-10-21'] = [['item_name'=>'XXX','fee'=>123],['item_name'=>'XXX','fee'=>235],['item_name'=>'XXX','fee'=>145]];
        $fee_list['2017-10-22'] = [['item_name'=>'XXX','fee'=>123],['item_name'=>'XXX','fee'=>235],['item_name'=>'XXX','fee'=>145]];
        $fee_list['2017-10-23'] = [['item_name'=>'XXX','fee'=>123],['item_name'=>'XXX','fee'=>235],['item_name'=>'XXX','fee'=>145]];
        $zyinfo['fee_list'] = $fee_list;
        return $this->returnSuccess($zyinfo);
        //        $options['jz_card']='0000000000347322';//模拟测试数据
        //        $options['beginDate']='2016-11-01';//模拟测试数据,查询进一个月的明细
        //        $options['endDate']='2016-12-21';//模拟测试数据
        //        dump($options);die;
        $options['beginDate']= date('Y-m-d',strtotime("-30 day"));//查询近一个月的明细
        $options['endDate']=date('Y-m-d');//
        
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        if(empty($options['zy_num'])){
            return $this->returnErorr('住院号不能为空');
        }
        $param['patCardType'] = 1;//1：院内诊疗卡 2：社保卡 3：医保卡 4：区域健康卡
        $param['patCardNo'] = $options['jz_card'];
        $param['admissionNum'] = $options['zy_num'];
        $param['beginDate'] = $options['beginDate'];
        $param['endDate'] = $options['endDate'];
        //        dump($param);die;
        $res = self::callWebservice( self::GET_ZYFEE, $param );
        //        dump($res);die;
        if(isset($res['resultCode'])&&$res['resultCode'] =='0'){
            //就诊人基本信息
            $data['hos_id'] = $options['hos_code'];
            $data['patient_id'] = $options['pat_id'];//就诊人ID
            $data['jz_card'] = $options['jz_card'];//患者ID号
            $data['zy_num'] = $options['zy_num'];//住院号
            
            //获取住院费用及住院余额
            $recordParam['hos_id']=$options['hos_code'];
            $recordParam['patient_id']=$options['pat_id'];
            $recordParam['jz_card']=$options['jz_card'];
            $recordParam['zy_num']=$options['zy_num'];
            $record = $this->getZyRecord($recordParam);
            $data['balance'] = $record['balance'];//余额
            $total_fee = $record['total_fee'];//住院总费用
            
            //费用按天汇总
            $resArr = array();
            if (isset($res ['item'][0])) {
                $resArr = $res ['item'];
            } else {
                $resArr[0] = $res ['item'];
            }
            $zyEeeList = array();
            foreach ($resArr as $k=>$v){
                $zyEeeList[$k]['item_name'] = $v['itemDate'];
                $zyEeeList[$k]['item_price'] = $v['itemTotalFee'];
                //                $total_fee+=$v['itemTotalFee'];
            }
            $data['total_fee'] = $total_fee;
            $data['item_list']=null;//$zyEeeList;
            return $this->returnSuccess($data);
        }else{
            $errMsg = '未查到费用信息, '.$res['resultMessage'];
            return $this->returnErorr($errMsg);
        }
    }
    
    /*
     * 获取某一条住院记录
     * $options['jz_card']就诊卡
     * $options['zy_num']住院号
     */
    public function getZyDetail($options=array()){
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        $Request['MethodName'] = 'ZyHospitalRecords';
        $Request['PatientID'] = $options['jz_card'];
        //         $Request['outpatient_no'] = $options['jz_card'];
        //         $Request['AdmissTimes'] = 0;
        $res = $this->call($Request);
//        dump($res);die;
        $zyList = array();
        if (isset($res['Result']) && $res['Result'] == 0) {
            return $this->returnSuccess([]);
        } else {
            if(isset($res[0])){
                $zyList = $res;
            }else{
                $zyList[0] = $res;
            }
            return $this->returnSuccess($zyList);
        }
    }
    /*
     * 住院服务-健康宣教
     * $options['jz_card']就诊卡
     * $options['zy_num']住院号
     */
    public function getZyHealthEducation($options=array()){
        $data = [];
        $zyinfo = [];
        $zyinfo['patient_id'] = $options['id'];
        $zyinfo['patient_name'] = $options['patient_name'];
        $zyinfo['jz_card'] = $options['jz_card'];
        $zyinfo['zy_num'] = $options['zy_num'];
        $zyinfo['title'] = "标题".$options['zy_num'];
        $zyinfo['content'] = "内容".$options['zy_num'];
        $zyinfo['status'] = 0;
        array_push($data, $zyinfo);
        $zyinfo = [];
        $zyinfo['patient_id'] = $options['id'];
        $zyinfo['patient_name'] = $options['patient_name'];
        $zyinfo['jz_card'] = $options['jz_card'];
        $zyinfo['zy_num'] = $options['zy_num'];
        $zyinfo['title'] = "标题".$options['zy_num'];
        $zyinfo['content'] = "内容".$options['zy_num'];
        $zyinfo['status'] = 1;
        array_push($data, $zyinfo);
        return $this->returnSuccess($data);
        
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        if(empty($options['zy_num'])){
            return $this->returnErorr('住院号不能为空');
        }
        $Request['MethodName'] = 'ZY_';
        $Request['PatientID'] = $options['jz_card'];
        $Request['zy_num'] = $options['zy_num'];
        $res = $this->call($Request);
        //        dump($res);die;
        $record = null;
        $resArr = array();
        if(isset($res[0])){
            $resArr = $res;
        }else{
            $resArr[0] = $res;
        }
        $data = [];
        if ($resArr) {
            foreach ($resArr as $value) {
                if ($value['Result'] == 1) {
                    array_push($data, $value);
                }
            }
        }
        $this->returnSuccess($data);
    }
    /*
     * 住院服务-健康宣教-阅读
     * $options['jz_card']就诊卡
     * $options['zy_num']住院号
     */
    public function readZyHealthEducation($options=array()){
        return $this->returnSuccess([]);
        
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        if(empty($options['zy_num'])){
            return $this->returnErorr('住院号不能为空');
        }
        if(empty($options['h_id'])){
            return $this->returnErorr('宣教ID不能为空');
        }
        $Request['MethodName'] = 'ZY_';
        $Request['PatientID'] = $options['jz_card'];
        $Request['zy_num'] = $options['zy_num'];
        $Request['h_id'] = $options['h_id'];
        $res = $this->call($Request);
        if ($res['Result'] == 1) {
            $this->returnSuccess($res);
        } else {
            $this->returnErorr($res['Error']);
        }
    }
    
    /*
     * 住院服务-住院服务内容
     * $options['jz_card']就诊卡
     * $options['zy_num']住院号
     */
    public function getZyService($options=array()){
        $zyinfo = [];
        $zyinfo['patient_id'] = $options['id'];
        $zyinfo['patient_name'] = $options['patient_name'];
        $zyinfo['jz_card'] = $options['jz_card'];
        $zyinfo['zy_num'] = $options['zy_num'];
        $zyinfo['title'] = "标题".$options['zy_num'];
        $zyinfo['content'] = "内容".$options['zy_num'];
        $zyinfo['status'] = 0;
        return $this->returnSuccess($zyinfo);
        
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        if(empty($options['zy_num'])){
            return $this->returnErorr('住院号不能为空');
        }
        $Request['MethodName'] = 'ZY_';
        $Request['PatientID'] = $options['jz_card'];
        $Request['zy_num'] = $options['zy_num'];
        $res = $this->call($Request);
        //        dump($res);die;
        $record = null;
        $resArr = array();
        if(isset($res[0])){
            $resArr = $res;
        }else{
            $resArr[0] = $res;
        }
        $data = [];
        if ($resArr) {
            foreach ($resArr as $value) {
                if ($value['Result'] == 1) {
                    array_push($data, $value);
                }
            }
        }
        $this->returnSuccess($data);
    }
    //创建住院充值订单
    public function createZyOrder($options=array()){
        //        $options['jz_card']='0000000000143334';//模拟测试数据
        $order['order_no'] = $this->genOrderNo($prefix='ZY');
        $order['user_id'] = $options['user_id'];
//        $order['hos_id'] = $options['hos_id'];
        $order['patient_id'] = $options['patient_id'];
        $order['patient_name'] = $options['patient_name'];
        $order['jz_card'] = $options['jz_card'];
        $order['full_money'] = $options['full_money'];
        $order['inpatientno'] = $options['inpatientno'];
        $order['admisstimes'] = $options['admisstimes'];
        $order['type'] = 1;//1：住院充值
        $fullOrder = new FullOrder();
        $fullOrder->data($order)->save();
        if($fullOrder->id){
            $order['id']=$fullOrder->id;
            return $this->returnSuccess($order);
        }else{
            return $this->returnErorr('创建订单失败');
        }
    }
    
    //充值住院费
    public function payZyOrder($options = array()){
        $fullOrder = new FullOrder();
        $orderInfo = $fullOrder->getOrderPayInfo($options['order_id']);
//        dump($orderInfo);die;
        $orderNo = $orderInfo['order_no'];
        system_log("----------开始处理儿童医院住院充值订单[$orderNo]支付-------------");

        //模拟处理
        /*$data['id'] = $orderInfo['id'];
        $data['his_order_no'] = $orderInfo['id'].'1234';//his支付订单号
        $data['status'] = 1;//支付成功
        $updateResult = $fullOrder->updateFullOrder($data);
        return self::returnSuccess($orderInfo);*/

        $payTime = $orderInfo['pay_time'];
        $Request['MethodName'] = 'ZyPayDeposit';
//        $Request['PatId'] = '000456369300';//$orderInfo['jz_card'];
        $Request['PatId'] = $orderInfo['jz_card'];
        $Request['Inpatient_no'] = $orderInfo['inpatientno']; //?
        $Request['Inhosp_Num'] = $orderInfo['admisstimes']; //?
        $Request['OrderNo'] = $orderInfo['transaction_id'];
        $Request['Amount'] = $orderInfo['full_money'];
        $Request['Cheque_type'] = '11'; //与医院人员确认支付类型
        system_log('开始调用儿童医院his接口,住院支付订单:'.json_encode($Request));
//        dump($Request);die;
        $res = $this->call($Request);
        system_log('his接口返回:'.json_encode($res));
        if(isset($res['Result'])&&$res['Result'] ==1){
            system_log("调用his接口支付成功,开始更新订单状态:");
            //修改数据库状态
            $data['id'] = $orderInfo['id'];
            $data['his_order_no'] = isset($res['hisOrdNum'])?$res['hisOrdNum']:0;//his支付订单号
            $data['status'] = 1;//支付成功
            $updateResult = $fullOrder->updateFullOrder($data);

            // 将支付信息再次通知默联
            try {

                $RequestMl['OrderNo'] = $orderInfo['ml_order_no'];
                $RequestMl['Hislsh'] = $orderInfo['transaction_id'];
                $DataTableMl['data'] = $RequestMl;
                $xml = new A2Xml();
                $paramsMl['xml'] = $xml->toXml($DataTableMl);
                // $data['funCode'] = 'F000120';
                $paramsMl['funCode'] = 'UpdateOrderInfoForLSH';
                system_log("儿童住院充值,通知默联请求:".json_encode($paramsMl));
//            dump($params);die;
                $resMl = $this->callMlPost($paramsMl);
                system_log("儿童住院充值,通知默联结果:".json_encode($resMl));

            } catch (\Exception $e) {
                system_log("默联通知出现异常".$e->getMessage());
            }

            if($updateResult){
                system_log("-------------支付成功,订单号[$orderNo]----------------");
                //发送微信模板消息
                $openid = $orderInfo['openid'];
                $tepParam['first'] = "住院充值成功";
                $tepParam['keyword1'] = $orderInfo['patient_name'];//就诊人
                $tepParam['keyword2'] = $orderInfo['inpatientno'];//住院号
                $tepParam['keyword3'] = $orderInfo['full_money'];
                $tepParam['remark'] = "昆明市儿童医院全体工作人员祝您早日康复，阖家幸福。'";
                $url = config('web_domain') . "/html/zy/paydetail.html?id={$options['order_id']}&type=2";
                try {
                    $app = new Application(config('wechat'));
                    $notice = $app->notice;
                    $messageId = $notice->send([
                        'touser' => $openid,
//                        'touser' => "o9U-Ft0voQiATFn4DSAmQt-jdFsE",
                        'template_id' => 'vgLlTbRLl7uHifjfBjOOHLKsDJpkz7mgJH3HBX67oZ0',
                        'url' => $url,
                        'data' => $tepParam,
                    ]);
                } catch (\Exception $e) {
                    system_log("支付成功,通知发送失败".$e->getMessage());
                }
                return self::returnSuccess($orderInfo);
            }else{
                system_log("支付成功,更新订单状态失败");
                return self::returnErorr('支付成功,更新订单状态失败');
            }
        }else{
            $errMsg = isset($res['ERROR'])?$res['ERROR']:'调用his支付失败';//错误消息
            $errInfo = '调his支付失败!订单号：['.$orderNo.'],his接口返回:'.$errMsg;
            system_log($errInfo);
            //更新订单状态
            $data['id']=$orderInfo['id'];
            $data['status']=-1;//-1:微信支付成功,his接口支付失败;1:支付成功
            $data['res_msg']= $errMsg;
            $fullOrder->updateFullOrder($data);
            
            //记录错误日志
            $errData['order_no'] = $orderNo;
            $errData['order_event'] = self::ZY_TRADE_TYPE_APY;//支付订单;
            $errData['event_info'] = $errInfo;
            model('ExceptionLog')->data($errData)->save();
            
            //给开发者发送系统错误信息
            $sendWechatData = [
                'first' => '住院充值异常',
                'keyword1' => date('Y-m-d H:i:s'),
                'keyword2' =>$errInfo,
                'remark' =>'请查看'
            ];
            $url = config('web_domain') . "/html/zy/paydetail.html?id={$options['order_id']}&type=1";
            $app = new Application(config('wechat'));
            $notice = $app->notice;
            $messageId = $notice->send([
                'touser' => self::$DEV_OPENID,
                'template_id' => 'r_0J1mrKCU-9oCObDE-nfdBn_7PPAfmC1YjLUnoZNuE',
                'url' => $url,
                'data' => $sendWechatData
            ]);

            return $this->returnErorr('调用his接口支付订单失败'.$errMsg);
        }
    }
    
    
    //查询住院费用清单明细--暂未使用
    public function getZyFeeDay($options=array()){
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        $Request['MethodName'] = 'ZY_GetPatChargeInfo';
        $Request['PatientID'] = $options['jz_card'];
//         $Request['outpatient_no'] = $options['zy_no'];
//         $Request['AdmissTimes'] = $options['zy_num'];
        $res = $this->call($Request);
        $zyList = array();
        if(isset($res[0])){
            $zyList = $res;
        }else{
            $zyList[0] = $res;
        }
        return $this->returnSuccess($zyList);
    }
    
    /*
     * 获取某一条住院记录
     * $options['jz_card']就诊卡
     * $options['zy_num']住院号
     */
    private function getZyRecord($options=array()){
        //        $options['jz_card'] = '0000000000347322';//模拟测试数据
        if(empty($options['jz_card'])){
            return $this->returnErorr('患者ID号不能为空');
        }
        if(empty($options['zy_num'])){
            return $this->returnErorr('住院号不能为空');
        }
        $param['patCardType'] = 1;//1：院内诊疗卡 2：社保卡 3：医保卡 4：区域健康卡
        $param['patCardNo'] = $options['jz_card'];
        $res = self::callWebservice( self::GET_ZY_LIST, $param );
        //        dump($res);die;
        $record = null;
        if(isset($res['resultCode'])&&$res['resultCode'] =='0'){
            $resArr = array();
            if(isset($res['item'][0])){
                $resArr = $res['item'];
            }else{
                $resArr[0] = $res['item'];
            }
            foreach ($resArr as $k=>$v){
                if($options['zy_num'] == $v['admissionNum']){
                    $record = array();
                    $record['hos_id'] = $options['hos_code'];
                    $record['patient_id'] = $options['pat_id'];
                    $record['jz_card'] = $options['jz_card'];
                    $record['zy_num'] = $v['admissionNum'];//住院号
                    $record['dep_name'] = $v['deptName'];//住院科室
                    $record['in_date'] = $v['inDate'];//入院日期
                    $totalFee = empty($v['totalFee'])?0:$v['totalFee'];//住院总费用
                    $payFee = empty($v['balance'])?0:$v['balance'];//住院押金余额
                    $record['total_fee'] = $v['totalFee'];
                    $record['balance'] = $payFee-$totalFee;//当前住院余额
                    $record['status'] = $v['status'];//1在院,2已出院
                }
            }
            return $record;
        }else{
            return null;
        }
    }
    
    /**
     * 对账
     * @param unknown $options
     */
    public function checkBill($options=array()) {
        $Request['MethodName'] = 'MZ_GetPayStatus';
        $Request['PatientID'] = $options['jz_card']; // 患者ID
        $Request['OutTradeNo'] = $options['order_no']; // 订单号
//         $Request['OutTradeNo'] = $options['transaction_id']; // 就诊人姓名
        $res = $this->call($Request);
        if (isset($res['Result']) && $res['Result'] == 1) {
            return self::returnSuccess($res);
        } else {
            return self::returnErorr($res['Error']);
        }
    }
    
    public function replaceMsg($msg) {
        $msg = str_replace($this->bk_soap_url, '', $msg);
        $msg = str_replace($this->mz_soap_url, '', $msg);
        $msg = str_replace($this->mzzf_soap_url, '', $msg);
        $msg = str_replace($this->zy_soap_url, '', $msg);
        $msg = str_replace($this->lis_soap_url, '', $msg);
        return $msg;
    }

    /*
     * 调用his接口查询门诊缴费订单信息
     */
    public function getMzErOrder($orderNo){
        $Request['MethodName'] = 'MzErOrder';
        $Request['OrderNo'] = $orderNo;
        system_log('门诊异常订单查询参数'.json_encode($Request));
        $res = $this->call($Request);
        system_log('门诊异常订单查询结果'.json_encode($res));
        if (isset($res['Result']) && $res['Result'] == 1) {
            return self::returnSuccess($res);
        } else {
            return self::returnErorr($res['Error']);
        }
    }

    /*
    * 调用his接口查询住院充值订单信息
    */
    public function getZyErOrder($orderNo){
        $Request['MethodName'] = 'ZyErOrder';
        $Request['OrderNo'] = $orderNo;
        system_log('住院异常订单查询参数'.json_encode($Request));
        $res = $this->call($Request);
        system_log('住院异常订单查询结果'.json_encode($res));
        if (isset($res['Result']) && $res['Result'] == 1) {
            return self::returnSuccess($res);
        } else {
            return self::returnErorr($res['Error']);
        }
    }


    /*
     * 调用his接口同步门诊退费订单
     */
    public function synMzRefundOrder($datetime=''){
        $Request['MethodName'] = 'MzRefundOrder';
        $Request['Datetime'] = !empty($datetime)?$datetime:date('Y-m-d',strtotime("-100 days"));
//        $Request['Datetime'] = "2019-10-22 09:26:29";
        $res = $this->call($Request);
        if (isset($res['Result']) && $res['Result'] == 0) {
            return self::returnErorr($res['Error']);
        } else {
            if(isset($res[0])){
                $resArr = $res;
            }else{
                $resArr[0] = $res;
            }
            return self::returnSuccess($res);
        }
    }

    /*
     * 调用his接口查询用户住院费用余额低于500的用户
     */
    public function getZyDepositMin(){
        $Request['MethodName'] = 'ZyDepositMin';
        $res = $this->call($Request);
        system_log('查询用户住院费用余额:'.json_encode($res,JSON_UNESCAPED_UNICODE));
        if (isset($res['Result']) && $res['Result'] == 0) {
            return self::returnErorr($res['Error']);
        } else {
            if(isset($res[0])){
                $resArr = $res;
            }else{
                $resArr[0] = $res;
            }
            return self::returnSuccess($res);
        }
    }
    function getDocScheduleData($options = array())
    {
        if (!isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
        }
        $hosId = $options['hos_code'];
        $Request['MethodName'] = 'BK_GetSchedule';
//         $Request['MethodName'] = 'BK_GetSchedule_Test';
        $Request['DeptNo'] = $this->hos_code_map[$hosId];
        $Request['DeptSn'] = $options['dep_id'];
//        $Request['DoctCode'] = $options['doc_id'];
        $Request['DoctCode'] = "";

        $Request['BeginDate'] = isset($options['from_date']) ? $options['from_date'] : '';
        $Request['EndDate'] = isset($options['end_date']) ? $options['end_date'] : '';
        // dump($Request);die;
        $response = $this->call($Request);
        if (isset($response['Result']) && $response['Result'] == 0) {
            return $this->returnErorr(isset($response['msg']) ? $response['msg'] : '未获取到医生排班');
        }

        try {
            cache('api_doc_schedule' . '_' . $hosId . '_' . $options['dep_id'] .  '_' . $options['from_date'] . '_' . $options['end_date'], $response, 5); // 将医生排班缓存5秒
        } catch (\Exception $e) {
        }
        return $response;
    }

        /*
     * 获取病人资料信息
     */
    public function searchPatientCardByJzCard($options = array()) {
        if (! isset($options['hos_code']) || empty($options['hos_code'])) {
            $options['hos_code'] = $this->hos_code;
            $options['p_id'] = $this->hos_code;
        }
        $Request = [];
        $Request['MethodName'] = 'BK_GetPatInfo';
        $Request['FindType'] = 3; // 查询类型:门诊ID
        $Request['FindNo'] = $options['jz_card'];
        
        $res = $this->call($Request);
 
        system_log('his原始返回：'.json_encode($res));
            if(!isset($res['Result']) && isset($res[0])) {
                $res = $res[0];
            }
            if (isset($res['Result']) && $res['Result'] == 1) {
                    $data = [];
                    $data['patient_name'] = $res['PatientName'];
                    if (isset($res['Sex'])) {
                        $data['patient_sex'] = $res['Sex'] == "男" ? 1 : 2;
                    } else {
                        $data['patient_sex'] = 0;
                    }
                    $data['patient_birthday'] = $res['Birthday'];
                    $data['patient_nation'] = isset($res['Nation'])?$res['Nation']:'';
                    $data['patient_address'] = $res['Address'];
                    $data['guarantee_name'] = isset($res['Relation'])?$res['Relation']:'';
                    $data['patient_relation'] = isset($res['RelationName'])?$res['RelationName']:'其他';
                    $data['patient_phone'] = isset($res['RelationTel'])?$res['RelationTel']:'';
                    $data['guarantee_phone'] = isset($res['RelationTel'])?$res['RelationTel']:'';
                    $data['patient_idcard'] = isset($res['social_code'])?$res['social_code']:''; // 患者身份证
                    $data['guarantee_idcard'] = isset($res['parent_social'])?$res['parent_social']:''; // 监护人身份证
                    $data['jz_card'] = $res['PatientID'];
                    return self::returnSuccess($data);
            } else {
                return self::returnErorr($this->replaceMsg($res['Error']));
            }
        
    }
}
?>
