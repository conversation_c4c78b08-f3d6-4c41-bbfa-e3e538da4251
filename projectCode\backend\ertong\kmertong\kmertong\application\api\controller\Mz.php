<?php

// +----------------------------------------------------------------------
// | 滇医通
// +----------------------------------------------------------------------
// | Copyright (c) 2017 http://www.ynhdkc.com All rights reserved.
// +----------------------------------------------------------------------
// | Author: 黄生俊 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\FullOrder;
use app\common\model\MiOrder;
use app\common\model\MzPayOrder;
use app\common\model\MzRefundOrder;
use app\common\model\Patient;
use app\common\model\PatientCard;
use app\common\service\AliMiPay;
use app\common\service\HosService;
use app\common\service\WechatMiPay;
use EasyWeChat\Foundation\Application;
use fast\Random;
use think\Config;
use think\Db;
use think\Response;

class Mz extends Api {

    /**
     * 获取门诊支付参数(支付宝小程序、经过默联)
     */
    public function mz_pay_ali($userid, $openid, $order_id) {
//        return $this->returnErorr("订单号缺失", 0);
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $options = [];
        $options['order_id'] = $order_id;

        $MzPayOrder = new MzPayOrder();
        $mzPayOrderInfo = $MzPayOrder->getBaseInfo($order_id);
        if ($mzPayOrderInfo['user_id'] != $userid) {
            return $this->returnErorr("用户认证失败，请重新授权1", 99);
        }
        if ($mzPayOrderInfo['status'] == 1 || $mzPayOrderInfo['status'] == -1) {
            return $this->returnErorr("您已经支付过，请到医院窗口查询", 0);
        }
        if ($mzPayOrderInfo['status'] == -2) {
            return $this->returnErorr("您已经退款，请到医院窗口查询", 0);
        }
        $hosService = new HosService($mzPayOrderInfo['hos_id']);
        $mzPayOrderInfo['openid'] = $openid;
        $mzPayOrderInfo['order_id'] = $order_id;
        $res = $hosService->getMzAliPayId($mzPayOrderInfo);
        return json($res);
    }

    // 前端支付完，再去查单
    public function mz_pay_ali_query($order_id,$userid, $openid){
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $MzPayOrder = new MzPayOrder();
        $mzPayOrderInfo = $MzPayOrder->getBaseInfo($order_id);
        if ($mzPayOrderInfo['user_id'] != $userid) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $item = Db::name('mz_pay_order_ext')->where(['id'=>$order_id])->find();
        if(empty($item)){
            return $this->returnErorr("未找到相关数据", 0);
        }
        $mzPayOrderInfo['tran_no'] = $item['tran_no'];
        $mzPayOrderInfo['trade_no'] = $item['trade_no'];
        $mzPayOrderInfo['openid'] = $item['openid'];

        $hosService = new HosService($mzPayOrderInfo['hos_id']);
        $res = $hosService->getMzAliPayQuery($mzPayOrderInfo);
        return json($res);
    }

    /**
     * 获取门诊卡支付参数
     *
     * @param int $userid
     *            用户ID
     * @param string $openid
     *            openid
     * @param int $order_id
     *            订单ID
     */
    public function mz_pay($userid, $openid, $order_id) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $options = [];
        $options['order_id'] = $order_id;

        $MzPayOrder = new MzPayOrder();
        $mzPayOrderInfo = $MzPayOrder->getBaseInfo($order_id);
        if ($mzPayOrderInfo['user_id'] != $userid) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        if ($mzPayOrderInfo['status'] == 1 || $mzPayOrderInfo['status'] == -1) {
            return $this->returnErorr("您已经支付过，请到医院窗口查询", 0);
        }
        if ($mzPayOrderInfo['status'] == -2) {
            return $this->returnErorr("您已经退款，请到医院窗口查询", 0);
        }
        $hosService = new HosService($mzPayOrderInfo['hos_id']);
        $mzPayOrderInfo['openid'] = $openid;
        $mzPayOrderInfo['order_id'] = $order_id;
        $res = $hosService->getMzWxPayId($mzPayOrderInfo);
        return json($res);
    }

    /**
     * 获取门诊卡支付参数
     *
     * @param int $userid
     *            用户ID
     * @param string $openid
     *            openid
     * @param int $order_id
     *            订单ID
     */
    public function mz_card_pay($userid, $openid, $order_id) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $request = $this->request;
        $options = [];
        $options['order_id'] = $order_id;
        // 门诊就诊卡缴费
        $MzPayOrder = new MzPayOrder();
        $hos_code = $MzPayOrder->getHosCode($options['order_id']);
        $hosService = new HosService($hos_code);
        $options['hos_code'] = $hos_code;
        $res = $hosService->cardPayMzOrder($options);
        return json($res);
    }
    public function full_pay() {
        $request = $this->request;
        $options = [];
        $options['hos_code'] = $request->get('hos_code', 0);
        $options['patient_id'] = $request->get('patient_id', 0);
        $Patient = new Patient();
        $patient_info = $Patient->getDetail($options['patient_id']);
        $options['user_id'] = $patient_info['user_id'];
        $PatientCard = new PatientCard();
        $options['jz_card'] = $PatientCard->getCard($options['patient_id'], $options['hos_code']);
        $options['patient_name'] = $patient_info['patient_name'];
        $options['full_money'] = $request->get('recharge', 0);
        $hosService = new HosService($options['hos_code']);
        $create_res = $hosService->createMzFullOrder($options);
        if ($create_res['code'] == 1) {
            $parm['order_id'] = $create_res['data']['id'];
            $r = $hosService->getMzChargePayId($parm);
            // $r['data']['order_no'] = $create_res['data']['order_no'];
            // $r['data']['type'] = 2;
            return json($r);
        } else {
            return json($create_res);
        }
    }
    public function rechargeList() {
        $request = $this->request;
        $user_id = $request->get('user_id', 0);
        if (! $user_id) {
            return $this->returnErorr('参数错误');
        }
        $index = $request->get('index', 0);
        $page_num = $request->get('page_num', 20);
        $FullOrder = new FullOrder();
        $res = $FullOrder->getRechargePageList($user_id, $index, $page_num);
        return $this->returnSuccess($res ? $res : []);
    }

    /**
     * 已缴费列表-指引单前置
     * 如果没有指定就诊人则返回当前用户所有就诊人缴费的门诊缴费列表
     *
     * @return \think\Response
     */
    public function payedInfoList($userid, $openid, $date, $hos_code=0, $pat_id=0) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (!$userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $PatientModel = new Patient();
        //$patientList = $PatientModel->getPatientList($userid, $pat_id);
        $patientList = $PatientModel->getLists($userid);
        if (empty($patientList)) {
            return $this->returnErorr("暂无数据");
        }
        foreach ($patientList as &$value) {
            $value['pat_id'] = $value['id'];
            if ($hos_code) {
                $value['hos_code'] = $hos_code;
            }
        }
        $params['jz_cards'] = $patientList;
        $params['date'] = $date;
        $hosService = new HosService($hos_code);
        $res = $hosService->getMzPayedInfo($params);
        if ($res['code'] == 1) {
            if ($hos_code) {
                // 如果指定医院
                $data = [];
                $unpayList = $res['data'];
                foreach ($unpayList as $unpay) {
                    if (!isset($unpay['hos_code']) || $unpay['hos_code'] == $hos_code) {
                        array_push($data, $unpay);
                    }
                }
                return $this->returnSuccess($data);
            } else {
                return $this->returnSuccess($res['data']);
            }
        } else {
            return $this->returnErorr($res['msg']);
        }
    }

    /**
     * 指引单
     *
     * @return \think\Response
     */
    public function payedInfoDetail($userid, $openid, $jzcard, $times, $account, $hos_code=0) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (!$userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $params['jz_card'] = $jzcard;
        $params['times'] = $times;
        $params['account'] = $account;
        $hosService = new HosService($hos_code);
        $res = $hosService->getMzPayedInfoDetail($params);
        if ($res['code'] == 1) {
                return $this->returnSuccess($res['data']);
        } else {
            return $this->returnErorr($res['msg']);
        }
    }

    /**
     * 待缴费列表
     * 如果没有指定就诊人则返回当前用户所有就诊人待缴费的门诊缴费列表
     *
     * @return \think\Response
     */
    public function unpayList($userid, $openid, $hos_code=0, $pat_id=0) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $PatientModel = new Patient();
        //$patientList = $PatientModel->getPatientList($userid, $pat_id);
        $patientList = $PatientModel->getLists($userid);
        if (empty($patientList)) {
            return $this->returnErorr("暂无数据");
        }
        foreach ($patientList as &$value) {
            $value['pat_id'] = $value['id'];
            if ($hos_code) {
                $value['hos_code'] = $hos_code;
            }
        }
        $hosService = new HosService($hos_code);
        $res = $hosService->getMzUnPayList($patientList);
        if ($res['code'] == 1) {
            if ($hos_code) {
                // 如果指定医院
                $data = [];
                $unpayList = $res['data'];
                foreach ($unpayList as $unpay) {
                    if (!isset($unpay['hos_code']) || $unpay['hos_code'] == $hos_code) {
                        array_push($data, $unpay);
                    }
                }
                return $this->returnSuccess($data);
            } else {
                return $this->returnSuccess($res['data']);
            }
        } else {
            return $this->returnErorr($res['msg']);
        }
    }
    /**
     * 已缴费列表
     * 如果没有指定就诊人则返回当前用户所有就诊人待缴费的门诊缴费列表
     *
     * @return \think\Response
     */
    public function payedOrderList($userid, $openid, $hos_code=0, $pat_id=0) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $PatientModel = new Patient();
        $patientCardIdList = $PatientModel->getPatientCardIdListByUserId($userid, $pat_id);
        $result = array();
        $MzPayOrderModel = new MzPayOrder();
        foreach ($patientCardIdList as $patient_card) {
            $list = $MzPayOrderModel->getListsByPatientCardId($userid, $patient_card['jz_card'], $hos_code);
            $result = array_merge($result, $list);
        }
        if ($result) {
            usort($result, function($a, $b) {
                return $b['pay_time'] - $a['pay_time'];
            });
            foreach ($result as &$order) {
                $order['pay_time_str'] = date("Y-m-d H:i", $order['pay_time']);
                $order['update_time_str'] = date("Y-m-d H:i", $order['update_time']);
            }
        }
        return $this->returnSuccess($result);
    }
    /**
     * 已缴费列表
     * 如果没有指定就诊人则返回当前用户所有就诊人待缴费的门诊缴费列表
     *
     * @return \think\Response
     */
    public function payedList($userid, $openid, $hos_code=0, $pat_id=0) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }

        $PatientModel = new Patient();
        //$patientList = $PatientModel->getPatientList($userid, $pat_id);
        $patientList = $PatientModel->getLists($userid);
        if (empty($patientList)) {
            return $this->returnErorr("暂无数据");
        }
        foreach ($patientList as &$value) {
            $value['pat_id'] = $value['id'];
            if ($hos_code) {
                $value['hos_code'] = $hos_code;
            }
        }
        $hosService = new HosService($hos_code);
        $res = $hosService->getMzPayList($patientList);
        if ($res['code'] == 1) {
            $data = $res['data'];
            foreach ($data as &$payed) {
                foreach ($patientList as $patientinfo) {
                    if ($payed['jz_card'] == $patientinfo['jz_card']) {
                        array_merge($payed, $patientinfo);
                    }
                }
            }
            if($data){
                return $this->returnSuccess($data);
            }else{
                return $this->returnErorr("暂无数据");
            }
        } else {
            return $this->returnErorr($res['msg']);
        }
    }

    /**
     * 显示指定的资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function payedDetail($userid, $openid, $id) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $obj = new \app\common\model\MzPayOrder();
        $item = $obj->getMzOrderPayInfo($id);
        if ($item) {
            $item['electronic_url'] = '';
            try {
                $hosService = new HosService($item['hos_id']);
                $electronicUrl = $hosService->getElectronicBill($item['transaction_id'], $item['jz_card'], $item['bill_id']);
//                $item['electronic_url'] = str_replace("192.168.9.65:30000", "220.165.247.116:9099", $electronicUrl);;
                $item['electronic_url'] = str_replace("192.168.9.65:30000", "wxtodz.etyy.cn:9099", $electronicUrl);;
            } catch (\Exception $e) {
                system_log('获取电子票据失败'.$e->getMessage());
            }

//             $hosService = new HosService($item['hos_id']);
//             $res = $hosService->payMzOrder(['order_id'=>$item['id']]);
            // if ($item['openid'] != $openid) {
            //     return $this->returnErorr("用户认证失败，请重新授权", 99);
            // }
            $item['pay_time'] = date('Y-m-d H:i:s', $item['pay_time']);
            if(empty($item['res_msg'])) {
                $item['res_msg'] = $this->mz_zhi_yin($item['order_no']);
            }
            if (isset($item['res_msg'])) {
                $item['res_msg'] = nl2br($item['res_msg']);
            }
            return $this->returnSuccess($item);
        } else {
            return $this->returnErorr("没有相关信息");
        }
    }
    /**
     * 显示指定的资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function cfDetail($userid, $openid, $id) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $obj = new \app\common\model\MzPayOrder();
        $item = $obj->getBaseInfo($id);
        if ($item&&$item['status']==1) {
            $hosService = new HosService();
            $res = $hosService->getMzCfDetail($item);
            if ($res['code'] == 1) {
                $data ['patient_name'] = $item['patient_name'];
                $data ['jz_card'] = $item['jz_card'];
                $data ['bill_time'] = $item['bill_time'];
                $data ['doc_name'] = $item['doc_name'];
                $data['cf_list'] = $res['data'];
                return $this->returnSuccess($data);
            } else {
                return $this->returnErorr($res['msg']);
            }
        }else{
            return $this->returnErorr("没有相关信息");
        }
    }
    /**
     * 显示指定的资源
     *
     * @param int $id
     * @return \think\Response
     */
    public function doPayDetail($userid, $openid, $id) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $obj = new \app\common\model\MzPayOrder();
        $item = $obj->getMzOrderPayInfo($id);
        if ($item) {
            $hosService = new HosService($item['hos_id']);
            $res = $hosService->payMzOrder(['order_id'=>$item['id']]);
            dump($res);exit;
            if ($item['openid'] != $openid) {
                return $this->returnErorr("用户认证失败，请重新授权", 99);
            }
            return $this->returnSuccess($item);
        } else {
            return $this->returnErorr("没有相关信息");
        }
    }
    /**
     * 获取分诊排队信息
     *
     * @return \think\Response
     */
    public function waitList($userid, $openid, $hos_code=0, $pat_id=0) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $PatientModel = new Patient();
        //$patientList = $PatientModel->getPatientList($userid, $pat_id);
        $patientList = $PatientModel->getLists($userid);
        if (empty($patientList)) {
            return $this->returnErorr("暂无数据");
        }
        $hosService = new HosService($hos_code);
        $result = [];
        if ($patientList) {
            foreach ($patientList as &$patient) {
                $res = $hosService->waitList($patient);
                if ($res['code'] == 1 && $res['data']) {
                    // 用卡数据上报
                    if($patient['tx_health_card']) {
                        $obj = new Txhealthcard();
                        $getCodeTextInfo = $obj->getQrCodeByWechatCodeInner($patient['tx_health_card'],$patient['patient_idcard'],$patient['idcard_type']);
                        if(isset($getCodeTextInfo['qrCodeText'])){
                            $posTx['qrCodeText'] = $getCodeTextInfo['qrCodeText'];
                            $posTx['time'] = date('Y-m-d H:i:s');
                            $posTx['hospitalCode'] = $obj->hospitalCode;
//            $posTx['subHospitalCode'] = '';
                            $posTx['scene'] = '0101021';//0101011-预约挂号，0101051-门诊缴费，0101081-查检查报告 0101021-排队候诊
//            $posTx['serviceId'] = '';
//                    $posTx['department'] = '';
                            $posTx['cardType'] = '11'; // 11 健康卡
                            $posTx['cardChannel'] = '0401'; // 服务号
//                            $posTx['cardCostTypes'] = '';
                            try {
                                system_log("queue up use card data");
                                $obj->reportHISData($posTx);
                            } catch (\Exception $e) {
                                // todo
                            }
                        }
                    }

                    foreach ($res['data'] as $data) {
                        //$data['pat_id'] = $patient['id'];
                        array_push($result, $data);
                    }
                    //$patient['wait_list'] = $res['data'];
                }
            }
        }

        model('Clicks')->recordsClick(date("Y-m-d"),'page','wait');
        if ($result) {
            return $this->returnSuccess($result);
        } else {
            return $this->returnErorr("未获取到排队信息");
        }
    }

    /**
     * 待缴费详细(点击支付，创建订单，返回订单信息)
     *
     * @param int $id
     * @return \think\Response
     */
    public function createMzOrder($userid, $openid, $pat_id, $bill_id, $hos_code = 0) {
        $res = $this->createMzOrderBase($userid, $openid, $pat_id, $bill_id);
        return json($res);
    }

    public function createMzMiOrderAli($userid, $openid, $pat_id, $bill_id, $isGT = 0) {
        $Patient = new \app\common\model\Patient();
        $patientInfo = $Patient->getDetailWithUserId($userid, $pat_id);
        if (!$patientInfo['patient_idcard']) {
            $this->returnErorr("就诊人无身份证号，无法使用医保移动支付");
        }
            return json($this->createMzOrderBase($userid, $openid, $pat_id, $bill_id, true, $isGT));
    }

    public function createMzMiOrder($userid, $openid, $pat_id, $bill_id, $mi_auth_code, $dev) {
        //获取微信免密授权参数
        $miPay = new WechatMiPay();
        $miAuthInfo = $miPay->getUserMedInfo($mi_auth_code, $openid);
        if ($miAuthInfo['code'] != 0) {
            system_log('微信医保支付免密授权失败，微信返回报文：'.json_encode($miAuthInfo) );
            return $this->returnErorr("微信医保支付免密授权失败，请刷新页面后重试或前往医院收费窗口进行缴费");
        }

        //
        Db::startTrans();
        try{
            $mzOrder = $this->createMzOrderBase($userid, $openid, $pat_id, $bill_id, true);
            $hosService = new HosService();
            $miOrder = $hosService->createMzMiPayOrder($mzOrder['data'], $miAuthInfo);
            // 提交事务
            Db::commit();
            $result = [];
            $result['mzPayOrder'] = $mzOrder;
            $result['miOrder'] = $miOrder;
            return $this->returnSuccess($result);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            system_log($e);
        }
        return $this->returnErorr("微信医保支付下单失败，请刷新页面后重试或前往医院收费窗口进行缴费");
    }

    public function mz_mi_pay($userid, $openid, $order_id) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $options = [];
        $options['openid'] = $openid;
        $options['client_ip'] = get_client_ip(0, true);

        $MzPayOrder = new MzPayOrder();
        $mzPayOrderInfo = $MzPayOrder->getBaseInfo($order_id);
        if ($mzPayOrderInfo['user_id'] != $userid) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        if ($mzPayOrderInfo['status'] == 1 || $mzPayOrderInfo['status'] == -1) {
            return $this->returnErorr("您已经支付过，请到医院窗口查询", 0);
        }
        if ($mzPayOrderInfo['status'] == -2) {
            return $this->returnErorr("您已经退款，请到医院窗口查询", 0);
        }
        $Patient = new Patient();
        $patient_info = $Patient->getDetail($mzPayOrderInfo['pat_id']);
        $options['patient_id_card'] = $patient_info['patient_idcard'];
        $options['patient_name'] = $patient_info['patient_name'];
        $options['order_id'] = $order_id;
        $miOrder = new MiOrder();
        $miOrderInfo = $miOrder->getBaseInfo($mzPayOrderInfo['order_no']);

        $miPay = new WechatMiPay();
        $res = $miPay->mzUnifiedOrder($options, $miOrderInfo);

        $miOrderInfo['pay_transaction_no'] = $res['med_trans_id'];
        $miOrderInfo['pay_info'] = $res['pay_url'];
        $miOrderInfo['order_status'] = 1;

        $miOrder->updateMiOrder($miOrderInfo);

        return $this->returnSuccess($res);
    }

    public function mz_zhi_yin($order_no) {
        $item = Db::name('mz_pay_order')->where(['order_no' => $order_no])->find();
        if(empty($item)) {
            return $this->returnErorr("订单不存在");
        }

        $params['jz_card'] = $item['jz_card'];
        $params['bill_id'] = $item['bill_id'];
        $params['mobile'] = $item['order_no'];
        $hosService = new HosService();
        $res = $hosService->getMzZhiYin($params);
        /*if(empty($res)){
            return $this->returnErorr("未获取到指引信息");
        }*/

        if(!empty($res)) {
            Db::name('mz_pay_order')->where(['order_no' => $order_no])->update(['res_msg'=>$res]);
        }
        return $res;

//        return $this->returnSuccess(['res_msg'=>$res]);
    }

    public function mz_mi_pay_ali($user_id, $openid, $order_id,$accessToken) {
        $userinfo = $this->getUserInfo($user_id, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $options = [];
        $options['openid'] = $openid;
        $options['client_ip'] = get_client_ip(0, true);

        $MzPayOrder = new MzPayOrder();
        $mzPayOrderInfo = $MzPayOrder->getBaseInfo($order_id);
        if ($mzPayOrderInfo['user_id'] != $user_id) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        if ($mzPayOrderInfo['status'] == 1 || $mzPayOrderInfo['status'] == -1) {
            return $this->returnErorr("您已经支付过，请到医院窗口查询", 0);
        }
        if ($mzPayOrderInfo['status'] == -2) {
            return $this->returnErorr("您已经退款，请到医院窗口查询", 0);
        }
        $Patient = new Patient();
        $patient_info = $Patient->getDetail($mzPayOrderInfo['pat_id']);
        $options['patient_id_card'] = $patient_info['patient_idcard'];
        $options['patient_name'] = $patient_info['patient_name'];
        $options['order_id'] = $order_id;
        $miOrder = new MiOrder();
        $miOrderInfo = $miOrder->getBaseInfo($mzPayOrderInfo['order_no']);

        $miPay = new AliMiPay();
        $res = $miPay->mzUnifiedOrder($options, $miOrderInfo,$accessToken);

//        $miOrderInfo['pay_transaction_no'] = $res['med_trans_id'];
//        $miOrderInfo['pay_info'] = $res['pay_url'];
//        $miOrderInfo['order_status'] = 1;
//
//        $miOrder->updateMiOrder($miOrderInfo);

        return $this->returnSuccess($res);
    }

    public function createMzOrderBase($userid, $openid, $pat_id, $bill_id, $isMiOrder = false, $isGT = 0) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        if (! $pat_id || ! $bill_id) {
            return $this->returnErorr("缺少参数,刷新页面再次尝试");
        }

        $PatientModel = new Patient();
        $patientInfo = $PatientModel->getDetail($pat_id);
        // 验证当前用户与就诊人关系
        if ($patientInfo['user_id'] != $userid) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }

        $hosService = new HosService();
        if ($isGT === 1) {
            $bill_id = $bill_id. '$GT'. Random::numeric();
        }
        $params = ['pat_id'=>$pat_id, 'bill_id'=>$bill_id, 'userid'=>$userid,'jz_card'=>$patientInfo['jz_card'], 'is_mi_order'=>$isMiOrder];
        $res = $hosService->createMzOrder($params);

        if($res['code'] == 1 && $patientInfo['tx_health_card']){
            $obj = new Txhealthcard();
            $getCodeTextInfo = $obj->getQrCodeByWechatCodeInner($patientInfo['tx_health_card'],$patientInfo['patient_idcard'],$patientInfo['idcard_type']);
            if(isset($getCodeTextInfo['qrCodeText'])){
                $posTx['qrCodeText'] = $getCodeTextInfo['qrCodeText'];
                $posTx['time'] = date('Y-m-d H:i:s');
                $posTx['hospitalCode'] = $obj->hospitalCode;
//            $posTx['subHospitalCode'] = '';
                $posTx['scene'] = '0101051';//0101011-预约挂号，0101051-门诊缴费，0101081-查检查报告
//            $posTx['serviceId'] = '';
//                    $posTx['department'] = '';
                $posTx['cardType'] = '11';
                $posTx['cardChannel'] = '0401';
                $posTx['cardCostTypes'] = '0100';
                try {
                    system_log("mz up use card data");
                    $obj->reportHISData($posTx);
                } catch (\Exception $e) {
                    // todo
                }
            }
        }
        return $res;
    }

    /**
     * 支付回调
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function paynotify() {
        $app = new Application(config('wechat'));
        $response = $app->payment->handleNotify(function ($notify, $successful) {
            debug('paynotifyStart');
            $log_name = "log/wxnotify/" . date('Ym/d') . ".log"; // log文件路径
            wfile($log_name, "【接收到的门诊订单缴费notify通知】:\n" . json_encode($notify->toArray()) . "\n");
            $notify_data = $notify->toArray();
            $order_no = $notify_data['out_trade_no'];
//             wfile($log_name, "order_no:" . $notify->out_trade_no . "\n");
//             wfile($log_name, "mch_id:" . $notify->mch_id . "\n");
            $MzPayOrder = new MzPayOrder();
            $order_info = $MzPayOrder->getOrderInfo($order_no, 2);
            wfile($log_name, "order_no:\n" . json_encode($order_info) . "\n");
            if (! $order_info) {
                wfile($log_name, "订单不存在,order_no:\n" . $notify->out_trade_no . "\n");
                //return "订单不存在";
                return true;
            } else if ($order_info['status'] == 0) {
                // 查询是否记录过订单通知
                $payOrder = model('PayOrder')->where(['order_no' => $order_no])->find();
                if (! $payOrder) { // 如果没有记录,将支付信息写入微信支付订单表
                    $WxPayOrderData['mch_id'] = $notify_data['mch_id']; // 商户ID
                    $WxPayOrderData['openid'] = $notify_data['openid']; // 用户openid
                    $WxPayOrderData['sub_mch_id'] = isset($notify_data['sub_mch_id']) ? $notify_data['sub_mch_id'] : ''; // 子商户ID
                    $WxPayOrderData['total_price'] = $order_info['total_price'];
                    $WxPayOrderData['real_pay_money'] = $notify_data['total_fee'] / 100; // 微信实际支付费用
                    $WxPayOrderData['order_no'] = $notify_data['out_trade_no']; // 商家订单号
                    $WxPayOrderData['transaction_id'] = $notify_data['transaction_id']; // 微信支付订单号
                    $WxPayOrderData['pay_time'] = strtotime($notify_data['time_end']);
//                     $WxPayOrderData['mch_id'] = $notify->data['mch_id']; // 商户ID
//                     $WxPayOrderData['openid'] = $notify->data['openid']; // 用户openid
//                     $WxPayOrderData['sub_mch_id'] = isset($notify->data['sub_mch_id']) ? $notify->data['sub_mch_id'] : ''; // 子商户ID
//                     $WxPayOrderData['total_price'] = $order_info['total_price'];
//                     $WxPayOrderData['real_pay_money'] = $notify->data['total_fee'] / 100; // 微信实际支付费用
//                     $WxPayOrderData['order_no'] = $notify->data['out_trade_no']; // 商家订单号
//                     $WxPayOrderData['transaction_id'] = $notify->data['transaction_id']; // 微信支付订单号
//                     $WxPayOrderData['pay_time'] = strtotime($notify->data['time_end']);
                    $WxPayOrderData['type'] = 1; // 0-挂号缴费,1-门诊缴费,2-门诊充值,3-住院充值
                    $WxPayOrderData['status'] = 1;
                    wfile($log_name, "WxPayOrderData:" . json_encode($WxPayOrderData) . "\n");
                    $wx_pay_id = model('PayOrder')->data($WxPayOrderData)->save();

                    // 调用各医院门诊缴费方法
                    wfile($log_name, "开始调用His接口[订单号:$order_no]" . "\n");
                    $hosService = new HosService($order_info['hos_id']);
                    $result = $hosService->payMzOrder(['order_id' => $order_info['id']]);
                    debug('paynotifyEnd');
                    if (config('app_debug')) {
                        system_log('调用预约接口耗时:' .debug('paynotifyStart','paynotifyEnd', 6).'s  订单号：'.$order_no);
                    }
                    if ($result['code'] == 1) {
                        wfile($log_name, "订单处理成功[订单号:$order_no]" . "\n");
                        return true;
                    } else {
                        wfile($log_name, "订单处理失败[订单号:$order_no]" . $result['msg'] . "\n");
                        return true;
                    }
                } else {
                    // 返回给微信已经处理了
                    return true;
                }
            } else {
                return true;
            }
        });
    }



    public function electronicBill($hospitalCode,$orderNo,$patientId,$billId) {


        $hosService = new HosService($hospitalCode);
        $result = $hosService->getElectronicBill($orderNo, $patientId, $billId);
        if(empty($result)) {
            echo '空';
        }
        dump($result);
    }


    /**
     * 支付回调
     *
     * @param \think\Request $request
     * @param int $id
     */
    public function paynotifyml() {

        $params = request()->param();


        $log_name = "log/wxnotifyml/" . date('Ym/d') . ".log"; // log文件路径
        wfile($log_name, "【接收到的门诊订单缴费notify通知】:\n" . json_encode($params) . "\n");

        if(!isset($params['RspCode']) || $params['RspCode'] != 0 || empty($params['MerBillNo']) || empty($params['TradeNo'])){
            return json(['code'=>0,'msg'=>'默联回调参数异常']);
        }

        debug('paynotifyStart');
        $order_no = $params['MerBillNo'];
        $MzPayOrder = new MzPayOrder();
        $order_info = $MzPayOrder->getOrderInfo($order_no, 2);
        wfile($log_name, "order_no:\n" . json_encode($order_info) . "\n");

        if (! $order_info) {
            wfile($log_name, "订单不存在,order_no:\n" . $order_no . "\n");
            return json(['code'=>0,'msg'=>'订单不存在']);
//            return true;
        } else if ($order_info['status'] == 0) {
            // 查询是否记录过订单通知
            $payOrder = model('PayOrder')->where(['order_no' => $order_no])->find();
            if (! $payOrder) { // 如果没有记录,将支付信息写入微信支付订单表
                $WxPayOrderData['mch_id'] = '1247170901'; // 商户ID
                $WxPayOrderData['openid'] = $order_info['openid']; // 用户openid
                $WxPayOrderData['sub_mch_id'] = isset($params['sub_mch_id']) ? $params['sub_mch_id'] : ''; // 子商户ID
                $WxPayOrderData['total_price'] = isset($params['OrderAmount']) ? $params['OrderAmount'] : 0;
                $WxPayOrderData['real_pay_money'] = isset($params['OrderAmount']) ? $params['OrderAmount'] : 0; // 微信实际支付费用
                $WxPayOrderData['order_no'] = $order_no; // 商家订单号
                $WxPayOrderData['transaction_id'] = isset($params['TradeNo']) ? $params['TradeNo'] : ''; // 微信支付订单号
                $WxPayOrderData['pay_time'] = time();
                $WxPayOrderData['type'] = 1; // 0-挂号缴费,1-门诊缴费,2-门诊充值,3-住院充值
                $WxPayOrderData['status'] = 1;
                wfile($log_name, "WxPayOrderData:" . json_encode($WxPayOrderData) . "\n");
                model('PayOrder')->data($WxPayOrderData)->save();

                // 调用各医院门诊缴费方法
                wfile($log_name, "开始调用His接口[订单号:$order_no]" . "\n");
                $hosService = new HosService($order_info['hos_id']);
                $result = $hosService->payMzOrder(['order_id' => $order_info['id']]);
                debug('paynotifyEnd');
                if (config('app_debug')) {
                    system_log('调用预约接口耗时:' .debug('paynotifyStart','paynotifyEnd', 6).'s  订单号：'.$order_no);
                }
                if ($result['code'] == 1) {
                    wfile($log_name, "订单处理成功[订单号:$order_no]" . "\n");
                    return json(['code'=>1,'msg'=>'通知his处理成功']);

                } else {
                    wfile($log_name, "订单处理失败[订单号:$order_no]" . $result['msg'] . "\n");
                    return json(['code'=>1,'msg'=>'通知his处理失败']);
                }
            } else {
                // 返回给微信已经处理了
                return json(['code'=>1,'msg'=>'已经通知了his']);
            }
        } else {
            return json(['code'=>0,'msg'=>'订单状态异常']);
        }
    }

    /**
     * 充值回调
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function chargenotify() {
        $app = new Application(config('wechat'));
        $response = $app->payment->handleNotify(function ($notify, $successful) {
            $log_name = "log/wxnotify/" . date('Ym/d') . ".log"; // log文件路径
            wfile($log_name, "【接收到的充值notify通知】:\n" . json_encode($notify->toArray()) . "\n");
            $order_no = $notify->data['out_trade_no'];
            $FullOrder = new FullOrder();
            $order_info = $FullOrder->getOrderInfo($order_no, 2);
            if (! $order_info) {
                return "订单不存在";
            } else if ($order_info['status'] == 0) {

                // 查询是否记录过订单通知
                $payOrder = model('PayOrder')->where(['order_no' => $order_no])->find();
                if (! $payOrder) { // 如果没有记录,将支付信息写入微信支付订单表
                    $WxPayOrderData['mch_id'] = $notify->data['mch_id']; // 商户ID
                    $WxPayOrderData['openid'] = $notify->data['openid']; // 用户openid
                    $WxPayOrderData['sub_mch_id'] = isset($notify->data['sub_mch_id']) ? $notify->data['sub_mch_id'] : ''; // 子商户ID
                    $WxPayOrderData['total_price'] = $order_info['full_money'];
                    $WxPayOrderData['real_pay_money'] = $notify->data['total_fee'] / 100; // 微信实际支付费用
                    $WxPayOrderData['order_no'] = $notify->data['out_trade_no']; // 商家订单号
                    $WxPayOrderData['transaction_id'] = $notify->data['transaction_id']; // 微信支付订单号
                    $WxPayOrderData['pay_time'] = strtotime($notify->data['time_end']);
                    $WxPayOrderData['type'] = 3; // 0-挂号缴费,1-门诊缴费,2-住院充值,3-门诊充值
                    $WxPayOrderData['status'] = 1;
                    $wx_pay_id = model('PayOrder')->data($WxPayOrderData)->save();
                }
                // 调用各医院门诊缴费方法
                wfile($log_name, "开始调用His接口[订单号:$order_no]" . "\n");
                $hosService = new HosService($order_info['hos_id']);
                $result = $hosService->payMzOrder(['order_id' => $order_info['id']]);
                if ($result['code'] == 1) {
                    wfile($log_name, "订单处理成功[订单号:$order_no]" . "\n");
                } else {
                    wfile($log_name, "订单处理失败[订单号:$order_no]" . $result['msg'] . "\n");
                }

                return true;
            } else {
                return true;
            }
        });
    }


    /**
     * 保存更新的资源
     *
     * @param \think\Request $request
     * @param int $id
     * @return \think\Response
     */
    public function getAppointList($userid, $openid) {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (! $userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        // 就诊卡不存在 获取就诊人信息
        $Patient = new \app\common\model\Patient();
        $pat_list = $Patient->getLists($userid);
        if ($pat_list) {
            $appoint_list = [];
            $hosService = new HosService('871002');
            foreach ($pat_list as $pat) {
                $res = $hosService->getBookRecord($pat);
                if ($res['code'] == 1) {
                    $appoint_list[$pat['jz_card']] = $res['data'];
                }
            }
            return $this->returnSuccess($appoint_list);
        }
        return $this->returnErorr("暂无记录");
    }


    /**
     * 同步门诊正常退费订单
     *
     */
    public function synMzRefundOrder(){
        set_time_limit(3*60);
        $service = new HosService();
//        $datetime = date('Y-m-d');
//        $datetime = '2019-12-04 09:30';
//        dump($datetime);die;
        $mzRefundOrder = new MzRefundOrder();
        $lastSynTime = $mzRefundOrder->max('create_time');
        $datetime = $lastSynTime?date("Y-m-d H:00",$lastSynTime):date('Y-m-d');
//        dump($datetime);die;
        $res = $service->synMzRefundOrder($datetime);
//        dump($res);die;
        if($res['code']==1){
            if (isset($res['data'][0])) {
                $resArr = $res['data'];
            } else {
                $resArr[0] = $res['data'];
            }
            $list = [];
            $synNum = 0;
            $allNum = count($resArr);
            //将退费订单同步到数据库
            $exitOrderArr = $mzRefundOrder->where(['his_refund_time'=>['>=',$datetime]])->column('concat(order_no,his_refund_time)');
            foreach ($resArr as $value){
                $temp =[];
                if(empty($value['OrderNo'])) continue;
                $hisRefundTime = date("Y-m-d H:i:s",strtotime($value['Charge_date']));
                if(in_array($value['OrderNo'].$hisRefundTime,$exitOrderArr)) continue;//存在重复的订单
                $temp['order_no'] = str_replace('‘','',$value['OrderNo']);
                $temp['his_order'] = $value['Receipt_sn'];
                $temp['his_refund_time'] = $hisRefundTime;
                $temp['refund_amount'] = $value['Je'];
                $temp['create_time'] = time();
                $list[] = $temp;
                if(count($list)==100){
                    $synNum  += $mzRefundOrder->insertAll($list);
                    $list = [];
                }
            }
            if(count($list)>0){
                $synNum  += $mzRefundOrder->insertAll($list);
            }
            return $this->returnSuccess([],"总记录:$allNum,同步记录:$synNum");
        }else{
            return $this->returnErorr($res['msg']);
        }
    }

    /*
     * 定时处理门诊退费的订单
     */
    public function opMzRefundOrder(){
        $mzRefundOrder = new MzRefundOrder();
        $needRefundList = $mzRefundOrder->where(['status'=>0,'handles'=>['<',3]])->select();
        if($needRefundList){
            foreach ($needRefundList as $refundorder){

            }
            return $this->returnSuccess();
        }else{
            return $this->returnSuccess();
        }
    }
}
