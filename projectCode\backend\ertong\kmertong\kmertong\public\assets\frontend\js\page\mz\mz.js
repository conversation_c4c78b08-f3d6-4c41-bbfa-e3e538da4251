define(['site', 'wechat', 'user', 'patient', 'hospital', 'qrcode'], function (site, wechat, user, patient, hospital, jqrcode, mz) {
    var Controller = {
        site: site,
        user: user,
        patient: patient,
        bindEvents: function () {
        },
        initPayDetail: function () {
            $.showLoading();
            var order_id = $.getUrlParam('id');
            if (!order_id) {
                $.hideLoading();
                $.alert("请指定要查看的门诊记录");
                return;
            }
            user.getMzPayedDetail(order_id, function (data, ret) {
                $.hideLoading();
                console.log(data);
                if (ret.code != 1) {
                    $.alert(ret.msg, function () {
                    });
                } else {
                    var template = "";
                    if (data.is_mi_order === 1) {
                        template = $('#template_payed_detail_mi').html();
                    } else {
                        template = $('#template_payed_detail').html();
                    }
                    var compiledTemplate = $.Template7.compile(template);
                    var $html = compiledTemplate(data);
                    $('#jsid-pay-detail').empty().append($html);
                    //$('#qrcodediv').qrcode("http://192.168.251.52:8080/sangfor/out_patient.jsp?p_id="+data.jz_card);
                    $('#qrcodediv').qrcode(data.jz_card);
                }
            }, this);

        },
        initCfDetail: function () {
            $.showLoading();
            var order_id = $.getUrlParam('id');
            if (!order_id) {
                $.hideLoading();
                $.alert("请指定要查看的门诊记录");
                return;
            }
            user.getMzCfDetail(order_id, function (data, ret) {
                $.hideLoading();
                console.log(data);
                if (ret.code != 1) {
                    $.alert(ret.msg, function () {
                    });
                } else {
                    var template = $('#template_cf_detail').html();
                    var compiledTemplate = $.Template7.compile(template);
                    var $html = compiledTemplate(data);
                    $('#jsid-pay-detail').empty().append($html);
                }
            }, this);

        },
        initWaitList: function () {
            $.showLoading();
            user.mzWaitList(function (data, ret) {
                $.hideLoading();
                console.log(data);
                if (ret.code != 1) {
                } else {
                    var template = $('#template_wait_list').html();
                    var compiledTemplate = $.Template7.compile(template);
                    var $html = compiledTemplate({wait_list: data});
                    $('#jsid-wait-list').empty().append($html);
                }
            }, this);
        },
        initCovid: function () {
            var scan = $.getUrlParam('scan');
            var hos_code = $.getUrlParam("hos_code");
            var dep_id = $.getUrlParam("dep_id");
            var type = $.getUrlParam("type");
            $.showLoading();
            var manager = this;
            patient.getPatient(function (patientList) {
                $.hideLoading();
                if (patientList) {
                    manager.patientList = patientList;
                    var template = $('#template_patient_list').html();
                    var compiledTemplate = $.Template7.compile(template);
                    var $html = compiledTemplate({patientList: patientList});
                    $('#jsid-patient-list').empty().append($html);
                    manager.bindEvents();
                }

                $(".p-item").click(function () {
                    $(this).siblings(".p-item").removeClass('sel');
                    $(this).addClass('sel');
                });

                $(".go-submit").click(function () {
                    if (!hos_code) {
                        $.alert("未知的院区信息");
                        return;
                    }
                    if (!dep_id) {
                        $.alert("未知的院区内位置");
                        return;
                    }
                    /*var hcard = $(".sel").data("hcard");
                    if (!hcard){
                        $.alert("该就诊人没有电子健康卡无法开单");
                        return
                    }*/

                    var pat_id = $(".sel").data("pat_id");
                    if (!pat_id) {
                        $.alert("未知的就诊人信息");
                        return;
                    }
                    let jz_card = $(".sel").data("jz_card");
                    // type  0 单采  1 混采
                    let reqData = {hos_code: hos_code, pat_id: pat_id, jz_card: jz_card, dep_id: dep_id, type: type};
                    user.createMzCovid(reqData, function (data, ret) {
                        if (ret.code == 1) {
                            site.redirect('unpaylist.html', {scan: "scan"});
                        } else {
                            $.alert(ret.msg ? ret.msg : "开单失败");
                        }
                    })
                });

            }, this);
        },
        initAntigen: function () {
            var scan = $.getUrlParam('scan');
            var hos_code = $.getUrlParam("hos_code");
            var dep_id = $.getUrlParam("dep_id");
            var type = $.getUrlParam("type");
            $.showLoading();
            var manager = this;
            patient.getPatient(function (patientList) {
                $.hideLoading();
                if (patientList) {
                    manager.patientList = patientList;
                    var template = $('#template_patient_list').html();
                    var compiledTemplate = $.Template7.compile(template);
                    var $html = compiledTemplate({patientList: patientList});
                    $('#jsid-patient-list').empty().append($html);
                    manager.bindEvents();
                }

                $(".p-item").click(function () {
                    $(this).siblings(".p-item").removeClass('sel');
                    $(this).addClass('sel');
                });

                $(".go-submit").click(function () {
                    if (!hos_code) {
                        $.alert("未知的院区信息");
                        return;
                    }
                    if (!dep_id) {
                        $.alert("未知的院区内位置");
                        return;
                    }
                    /*var hcard = $(".sel").data("hcard");
                    if (!hcard){
                        $.alert("该就诊人没有电子健康卡无法开单");
                        return
                    }*/

                    var pat_id = $(".sel").data("pat_id");
                    if (!pat_id) {
                        $.alert("未知的就诊人信息");
                        return;
                    }
                    let jz_card = $(".sel").data("jz_card");
                    // type  0 单采  1 混采
                    let reqData = {hos_code: hos_code, pat_id: pat_id, jz_card: jz_card, dep_id: dep_id, type: type};
                    user.createMzAntigen(reqData, function (data, ret) {
                        if (ret.code == 1) {
                            site.redirect('unpaylist.html', {scan: "scan"});
                        } else {
                            $.alert(ret.msg ? ret.msg : "开单失败");
                        }
                    })
                });

            }, this);
        },
        initUnpayList: function () {
            var scan = $.getUrlParam("scan");
            $.showLoading();
            // 尝试获取医保支付参数
            var billId = $.getUrlParam("billId");
            var patId = $.getUrlParam("patId");
            var retCode = $.getUrlParam("retCode");
            var authCode = $.getUrlParam("authCode");
            var isGT = $.getUrlParam("isGt");
            console.log(window.location.href);
            if (billId && patId && retCode && authCode && retCode) {
                if (retCode !== "0") {
                    alert("未能成功获取到医保移动支付授权，请重试");
                } else {
                    // 医保支付参数存在则直接进入下单流程
                    console.log("进入医保下单流程", billId, patId, retCode, authCode, isGT);
                    user.createMzMiOrder(billId, patId, authCode, function (data) {
                        console.log(data);
                        var order_id = data.mzPayOrder.data.id;
                        var template = $('#medical_insurance_pay_detail').html();
                        var compiledTemplate = $.Template7.compile(template);
                        var $html = compiledTemplate(data.miOrder);
                        $('#jsid-bill-detail').empty().append($html);
                        $('#jsid-bill-detail-dialog').popup();
                        $.hideLoading();
                        $('#go-mi-pay').on('click', function () {
                            $.showLoading();
                            user.getMzMiPay(order_id, function (data) {
                                $.hideLoading();
                                console.log(data);
                                window.location.href = data.pay_url;
                            });
                        });
                    });
                    return;
                }
            }
            user.getMzUnpayList(function (data, ret) {
                // console.log(data);
                if (ret.code != 1) {
                    alert("未查询到待缴费记录，请稍后再试");
                    $.hideLoading();
                } else {
                    $('#jsid-unpaylist').empty();
                    $.each(data, function (i, item) {
                        if (!scan && item.doc_name == "核酸自助开单") return;
                        var template = $('#template_unpay_list').html();
                        var compiledTemplate = $.Template7.compile(template);
                        var $html = compiledTemplate(item);
                        $('#jsid-unpaylist').append($html);
                    });

                    /*
                    var template = $('#template_unpay_list').html();
                    var compiledTemplate = $.Template7.compile(template);
                    var $html = compiledTemplate({"unpaylist":data});
                    $('#jsid-unpaylist').empty().append($html);
                    */
                    $('.mz-unpay-item').off("click").on('click', function () {
                        // console.log('mz-unpay-item click');
                        var bill_id = $(this).data('bill-id');
                        var pat_id = $(this).data('pat-id');
                        var dep_name = $(this).data('dep_name');
                        var isGT = $(this).data('isGT')
                        if (bill_id && pat_id) {

                            const first = new Promise((resolve, reject) => {
                                patient.getPatientById(pat_id, function (info, ret) {
                                    resolve(info)
                                });
                            });

                            first.then((patient_info => {
                                var tag = (dep_name.indexOf('H') == -1 && dep_name.indexOf('发热') == -1) ? true : false;

                                if (patient_info.tx_health_card == "" && tag) {
                                    var n1 = new Date(patient_info.patient_birthday.replace(/-/g, "/"));//小于182不用电子健康卡
                                    var n2 = new Date();//置或显示系统日期
                                    var n = n2.getTime() - n1.getTime();
                                    if (n / 1000 / 60 / 60 / 24 > 182) {
                                        $.confirm({
                                            title: "",
                                            text: "该就诊人还没有电子健康卡，请先申领电子健康卡，若无法线上申领的，请去医院患者服务中心进行现场办理！",
                                            onOK: function () {
                                                site.redirect('patient', {});
                                            }
                                        });
                                        $.hideLoading();
                                        return;
                                    }

                                }


                                var find = false;
                                $.each(data, function (i, item) {
                                    if (item.bill_id == bill_id && item.patient_id == pat_id) {
                                        if (find) {
                                            return;
                                        } else {
                                            find = true;
                                        }
                                        console.log(item.jz_card);
                                        if (item.jz_card === '17339991' || item.jz_card == '18936182' || item.jz_card == '19695567') {
                                            item.isTestUser = true
                                        } else {
                                            item.isTestUser = false
                                        }
                                        var template = $('#template_unpay_item').html();
                                        var compiledTemplate = $.Template7.compile(template);
                                        var $html = compiledTemplate(item);
                                        $('#jsid-bill-detail').empty().append($html);
                                        $('#jsid-bill-detail-dialog').popup();
                                        $('#btn-mi-pay').on('click', function () {
                                            if (window.location.href.indexOf("tertong") > -1) {
                                                //跳转医保授权页面测试环境
                                                window.location.href = "https://mitest.wecity.qq.com/openAuth/info?authType=2&isDepart=2&appid=wx3df855bf9977df7b&cityCode=530100&channel=AAGQ3Cq_5-d68zT1q6wsAkas&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxtAArJrg4G9eEkxWJ1WYkXp&orgCodg=H53011200038&bizType=04107&orgAppId=1GA8HL0GN04O3F60C80A00006F92EC1B&redirectUrl=https%3A%2F%2Ftertong.ynhdkc.com%2Fhtml%2Fmz%2Funpaylist.html%3FbillId%3D" + bill_id + "%26patId%3D" + pat_id + "%26isGT%3D" + isGT;
                                            } else {
                                                //跳转医保授权页面正式环境
                                                window.location.href = "https://mitest.wecity.qq.com/openAuth/info?authType=2&isDepart=2&appid=wx3df855bf9977df7b&cityCode=530100&channel=AAGQ3Cq_5-d68zT1q6wsAkas&orgChnlCrtfCodg=BqK1kMStlhVDgN2uHf4EsLK%2FF2LjZPYJ81nK2eYQqxtAArJrg4G9eEkxWJ1WYkXp&orgCodg=H53011200038&bizType=04107&orgAppId=1GA8HL0GN04O3F60C80A00006F92EC1B&redirectUrl=https%3A%2F%2Fertong.ynhdkc.com%2Fhtml%2Fmz%2Funpaylist.html%3FbillId%3D" + bill_id + "%26patId%3D" + pat_id + "%26isGT%3D" + isGT;
                                            }
                                            return;
                                        });
                                        $('#btn-pay').off("click").on('click', function () {
                                            $.showLoading();
                                            user.createMzOrder(bill_id, pat_id, function (data, ret) {
                                                if (ret.code != 1) {
                                                    $.alert(ret.msg, function () {
                                                        $.hideLoading();
                                                    })
                                                } else {
                                                    var order_id = data.id;
                                                    if (!order_id) {
                                                        $.alert('系统正忙，请刷新后再操作', function () {
                                                            $.hideLoading();
                                                        })
                                                        return;
                                                    }
                                                    user.getMzPay(order_id, function (data, ret) {
                                                        $.hideLoading();
                                                        if (ret.code != 1) {
                                                            $.alert(ret.msg, function () {

                                                            })
                                                        } else {

                                                            var form = document.createElement('form');
                                                            form.setAttribute('method', 'POST');
                                                            form.setAttribute('id', 'goMolian');
                                                            // form.setAttribute('action','http://dev-molian.ynhdkc.com/MuWapPaymentsV3.aspx');
                                                            form.setAttribute('action', 'http://ertongpay.etyy.cn/MuWapPaymentsV3.aspx');
                                                            if (pat_id == 2557778) {
                                                                form.setAttribute('action', 'http://ertongpay.etyy.cn/MuWapPaymentsV3.aspx');
                                                            }

                                                            var MerBillNo = document.createElement('input');
                                                            MerBillNo.setAttribute('name', 'MerBillNo');
                                                            MerBillNo.setAttribute('value', ret.data.OrderNo);
                                                            form.appendChild(MerBillNo);

                                                            var UserId = document.createElement('input');
                                                            UserId.setAttribute('name', 'UserId');
                                                            UserId.setAttribute('value', pat_id);
                                                            form.appendChild(UserId);

                                                            var ValidTime = document.createElement('input');
                                                            ValidTime.setAttribute('name', 'ValidTime');
                                                            ValidTime.setAttribute('value', '');
                                                            form.appendChild(ValidTime);

                                                            var MerNo = document.createElement('input');
                                                            MerNo.setAttribute('name', 'MerNo');
                                                            MerNo.setAttribute('value', ret.data.MerNo);
                                                            form.appendChild(MerNo);


                                                            var ProductId = document.createElement('input');
                                                            ProductId.setAttribute('name', 'ProductId');
                                                            ProductId.setAttribute('value', "-");
                                                            form.appendChild(ProductId);

                                                            var ProductName = document.createElement('input');
                                                            ProductName.setAttribute('name', 'ProductName');
                                                            ProductName.setAttribute('value', "门诊缴费");
                                                            form.appendChild(ProductName);


                                                            var OrderDate = document.createElement('input');
                                                            OrderDate.setAttribute('name', 'OrderDate');
                                                            OrderDate.setAttribute('value', ret.data.OrderDate);
                                                            form.appendChild(OrderDate);


                                                            var OrderAmount = document.createElement('input');
                                                            OrderAmount.setAttribute('name', 'OrderAmount');
                                                            OrderAmount.setAttribute('value', ret.data.OrderAmount);
                                                            form.appendChild(OrderAmount);

                                                            var OrdSourceIp = document.createElement('input');
                                                            OrdSourceIp.setAttribute('name', 'OrdSourceIp');
                                                            OrdSourceIp.setAttribute('value', "");
                                                            form.appendChild(OrdSourceIp);


                                                            var CurrencyType = document.createElement('input');
                                                            CurrencyType.setAttribute('name', 'CurrencyType');
                                                            CurrencyType.setAttribute('value', "RMB");
                                                            form.appendChild(CurrencyType);


                                                            var PayAppCode = document.createElement('input');
                                                            PayAppCode.setAttribute('name', 'PayAppCode');
                                                            PayAppCode.setAttribute('value', "04");
                                                            form.appendChild(PayAppCode);


                                                            var PaySourceCode = document.createElement('input');
                                                            PaySourceCode.setAttribute('name', 'PaySourceCode');
                                                            PaySourceCode.setAttribute('value', "04");
                                                            form.appendChild(PaySourceCode);


                                                            var PayAccount = document.createElement('input');
                                                            PayAccount.setAttribute('name', 'PayAccount');
                                                            PayAccount.setAttribute('value', ret.data.MerNo);
                                                            form.appendChild(PayAccount);


                                                            var SignMD5 = document.createElement('input');
                                                            SignMD5.setAttribute('name', 'SignMD5');
                                                            SignMD5.setAttribute('value', ret.data.SignMD5);
                                                            form.appendChild(SignMD5);


                                                            url = window.location.protocol + "//" + window.location.host + "/html/mz/paydetail.html?type=1&id=" + ret.data.order_id;
                                                            var Merchanturl = document.createElement('input');
                                                            Merchanturl.setAttribute('name', 'Merchanturl');
                                                            Merchanturl.setAttribute('value', url);
                                                            form.appendChild(Merchanturl);


                                                            var S2SMerchanturl = document.createElement('input');
                                                            S2SMerchanturl.setAttribute('name', 'S2SMerchanturl');
                                                            S2SMerchanturl.setAttribute('value', ret.data.S2SMerchanturl);
                                                            form.appendChild(S2SMerchanturl);

                                                            var openidInput = document.createElement('input');
                                                            openidInput.setAttribute('name', 'openid');
                                                            openidInput.setAttribute('value', ret.data.openid);
                                                            form.appendChild(openidInput);

                                                            /*var submit = document.createElement('button');
                                                            submit.setAttribute("type", "submit");
                                                            submit.innerHTML = "sub";
                                                            form.appendChild(submit);*/

                                                            $('#insertMolian').append(form);
                                                            $('#goMolian').submit();


                                                            // document.querySelector('page__bd page__bd_spacing').appendChild(form)

                                                            /*// 请求默联 支付宝生活号、微信公众号支付
                                                            var requestMlData={'MerBillNo':ret.data.OrderNo,
                                                                'UserId':pat_id,
                                                                'ValidTime':'',
                                                                'MerNo':ret.data.MerNo,
                                                                'ProductId': '-',
                                                                'ProductName': '门诊缴费',
                                                                'OrderDate': ret.data.OrderDate,
                                                                'OrderAmount': ret.data.OrderAmount,
                                                                'OrdSourceIp': '',
                                                                'CurrencyType': 'RMB',
                                                                'PayAppCode': '04',
                                                                'PaySourceCode': '04',
                                                                'PayAccount': ret.data.MerNo,
                                                                'SignMD5': ret.data.SignMD5,
                                                                'Merchanturl': ret.data.Merchanturl,
                                                                'S2SMerchanturl': ret.data.S2SMerchanturl
                                                            };
                                                            user.getMolianPayUrl(requestMlData,function (mlRes) {
                                                                var iframe = document.createElement("iframe")
                                                                iframe.setAttribute("src","")
                                                                console.log(mlRes)
                                                            })*/


                                                            /*wechat.wxpay(data,function(res) {
                                                                //get_brand_wcpay_request
                                                                if (res.errMsg == 'chooseWXPay:ok' || res.errMsg == 'get_brand_wcpay_request:ok') {
                                                                    this.pay_appoint_loading = false;
                                                                    $.alert("支付成功", function() {
                                                                        //location.reload(true);
                                                                        site.redirect('mzpaydetail', {id:data.order_id});
                                                                        //location.href = "paydetail.html?type=1&id="+data.order_id;
                                                                    });
                                                                } else {
                                                                    $.alert(res.errMsg,function() {
                                                                        alert('家长您好，系统正为您重新进行授权，请在授权后进行缴费，如还不能缴费，请联系医院医导工作人员');
                                                                        this.pay_appoint_loading = false;
                                                                        site.gotoAouth();
                                                                    });
                                                                }
                                                            },this);*/
                                                            /*
                                                            wx.chooseWXPay({
                                                                timestamp: data.timestamp, // 支付签名时间戳，注意微信jssdk中的所有使用timestamp字段均为小写。但最新版的支付后台生成签名使用的timeStamp字段名需大写其中的S字符
                                                                nonceStr: data.nonceStr, // 支付签名随机串，不长于 32 位
                                                                package: data.package, // 统一支付接口返回的prepay_id参数值，提交格式如：prepay_id=***）
                                                                signType: data.signType, // 签名方式，默认为'SHA1'，使用新版支付需传入'MD5'
                                                                paySign: data.paySign, // 支付签名
                                                                success: function (res) {
                                                                    // 支付成功后的回调函数
                                                                    // 显示支付成功信息
                                                                    $.alert("支付成功", function() {
                                                                        location.reload(true);
                                                                         //location.href = "../payorder/detail.html?type=1&id="+data.order_no;
                                                                    });
                                                                }
                                                            });
                                                            */
                                                        }
                                                    }, this);


                                                }
                                            }, this);
                                        });
                                    }
                                });
                                //location.href = "../appoint/detail.html?id=" + order_id;

                            }));
                        }
                    });
                    $.hideLoading();
                }
            }, this);
            user.getMzPayedList(function (data, ret) {
                if (ret.code != 1) {
                } else {
                    if (data && data.length > 0) {
                        var template = $('#template_payed_list').html();
                        var compiledTemplate = $.Template7.compile(template);
                        var $html = compiledTemplate({payed_list: data});
                        $('#jsid-payedlist').empty().append($html);
                        $('.payed-bill').on('click', function () {
                            var order_id = $(this).data('order-id');
                            if (order_id) {
                                site.redirect('mzpaydetail', {id: order_id});
                            }
                        });
                    }
                }
            }, this);
        },
    };
    return Controller;
});
