---------------------------------------------------------------
[ 2025-06-13T09:53:03+08:00 ] 172.18.0.1 GET et.com/api/hospital/proDoctorList?hos_code=871002
[ system ] 调用专家列表接口出错：结果{"code":0,"msg":"\u6682\u65e0\u6392\u73ed"} 参数：{"MethodName":"BK_GetProSchedule","DeptSn":"","DoctCode":"","DeptNo":"1","AccessKey":"95B94EDB1A0E0B20F603","Organization":1,"BeginDate":"","EndDate":""}
---------------------------------------------------------------
[ 2025-06-13T10:17:09+08:00 ] 172.18.0.1 GET et.com/api/hospital/proDoctorList?hos_code=871002
[ system ] 调用专家列表接口出错：结果{"code":0,"msg":"\u6682\u65e0\u6392\u73ed"} 参数：{"MethodName":"BK_GetProSchedule","DeptSn":"","DoctCode":"","DeptNo":"1","AccessKey":"95B94EDB1A0E0B20F603","Organization":1,"BeginDate":"","EndDate":""}
---------------------------------------------------------------
[ 2025-06-13T10:19:21+08:00 ] 172.18.0.1 GET et.com/api/refactor_reg/doctorList?hos_code=871002&dep_id=3003&from_date=2025-6-13&end_date=2025-6-17&_=1749781158414
[ system ] 获取医生列表{"DataTable":[{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-13","WeekName":"\u661f\u671f\u4e94","TotalAmount":"65","RemainAmount":"0","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"0230","doctor_name":"\u4faf\u7476","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-13||3003|0230|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-13","WeekName":"\u661f\u671f\u4e94","TotalAmount":"70","RemainAmount":"20","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"0618","doctor_name":"\u6768\u9752","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-13||3003|0618|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-14","WeekName":"\u661f\u671f\u516d","TotalAmount":"70","RemainAmount":"54","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"0618","doctor_name":"\u6768\u9752","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-14||3003|0618|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-14","WeekName":"\u661f\u671f\u516d","TotalAmount":"30","RemainAmount":"30","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"2296","doctor_name":"\u5b8b\u6625\u8273","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-14||3003|2296|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-14","WeekName":"\u661f\u671f\u516d","TotalAmount":"40","RemainAmount":"25","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"4120","doctor_name":"\u8096\u7956\u521a","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-14||3003|4120|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-15","WeekName":"\u661f\u671f\u65e5","TotalAmount":"30","RemainAmount":"29","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"540","doctor_name":"\u96f7\u5e86\u9f84","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-15||3003|540|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-16","WeekName":"\u661f\u671f\u4e00","TotalAmount":"30","RemainAmount":"30","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"3100","doctor_name":"\u674e\u7fe0\u83b2","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-16||3003|3100|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-16","WeekName":"\u661f\u671f\u4e00","TotalAmount":"30","RemainAmount":"30","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"6927","doctor_name":"\u674e\u8273\u82b3","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-16||3003|6927|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-17","WeekName":"\u661f\u671f\u4e8c","TotalAmount":"45","RemainAmount":"43","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"0230","doctor_name":"\u4faf\u7476","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-17||3003|0230|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-17","WeekName":"\u661f\u671f\u4e8c","TotalAmount":"40","RemainAmount":"40","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"4120","doctor_name":"\u8096\u7956\u521a","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-17||3003|4120|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-17","WeekName":"\u661f\u671f\u4e8c","TotalAmount":"30","RemainAmount":"30","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"6927","doctor_name":"\u674e\u8273\u82b3","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-17||3003|6927|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"}]}
---------------------------------------------------------------
[ 2025-06-13T10:23:12+08:00 ] 172.18.0.1 GET et.com/api/refactor_reg/doctorList?hos_code=871002&dep_id=3003&from_date=2025-6-13&end_date=2025-6-17&_=1749781389670
[ system ] BK_GetScheduleNew请求入参:<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CallInterface>
            <!--Optional:-->
            <tem:serviceId><![CDATA[BK_GetScheduleNew]]></tem:serviceId>
            <!--Optional:-->
            <tem:msgBody><![CDATA[<?xml version="1.0" encoding="UTF-8"?>
<root><AccessKey>95B94EDB1A0E0B20F603</AccessKey><Organization>1</Organization><DeptNo>1</DeptNo><DeptSn>3003</DeptSn><DoctCode></DoctCode></root>]]>
            </tem:msgBody>
        </tem:CallInterface>
    </soapenv:Body>
</soapenv:Envelope>
[ system ] BK_GetScheduleNew请求出参:<?xml version="1.0" encoding="utf-8"?><soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema"><soap:Body><CallInterfaceResponse xmlns="http://tempuri.org/"><CallInterfaceResult>﻿&lt;root&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-13&lt;/ReservationDate&gt;&lt;WeekName&gt;星期五&lt;/WeekName&gt;&lt;TotalAmount&gt;65&lt;/TotalAmount&gt;&lt;RemainAmount&gt;0&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;0230&lt;/doctor_code&gt;&lt;doctor_name&gt;侯瑶&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-13||3003|0230|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-14&lt;/ReservationDate&gt;&lt;WeekName&gt;星期六&lt;/WeekName&gt;&lt;TotalAmount&gt;30&lt;/TotalAmount&gt;&lt;RemainAmount&gt;30&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;2296&lt;/doctor_code&gt;&lt;doctor_name&gt;宋春艳&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-14||3003|2296|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-14&lt;/ReservationDate&gt;&lt;WeekName&gt;星期六&lt;/WeekName&gt;&lt;TotalAmount&gt;40&lt;/TotalAmount&gt;&lt;RemainAmount&gt;25&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;4120&lt;/doctor_code&gt;&lt;doctor_name&gt;肖祖刚&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-14||3003|4120|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-15&lt;/ReservationDate&gt;&lt;WeekName&gt;星期日&lt;/WeekName&gt;&lt;TotalAmount&gt;30&lt;/TotalAmount&gt;&lt;RemainAmount&gt;29&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;540&lt;/doctor_code&gt;&lt;doctor_name&gt;雷庆龄&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-15||3003|540|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-16&lt;/ReservationDate&gt;&lt;WeekName&gt;星期一&lt;/WeekName&gt;&lt;TotalAmount&gt;30&lt;/TotalAmount&gt;&lt;RemainAmount&gt;30&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;6927&lt;/doctor_code&gt;&lt;doctor_name&gt;李艳芳&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-16||3003|6927|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-17&lt;/ReservationDate&gt;&lt;WeekName&gt;星期二&lt;/WeekName&gt;&lt;TotalAmount&gt;45&lt;/TotalAmount&gt;&lt;RemainAmount&gt;43&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;0230&lt;/doctor_code&gt;&lt;doctor_name&gt;侯瑶&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-17||3003|0230|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-17&lt;/ReservationDate&gt;&lt;WeekName&gt;星期二&lt;/WeekName&gt;&lt;TotalAmount&gt;40&lt;/TotalAmount&gt;&lt;RemainAmount&gt;40&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;4120&lt;/doctor_code&gt;&lt;doctor_name&gt;肖祖刚&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-17||3003|4120|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;DataTable&gt;&lt;Result&gt;1&lt;/Result&gt;&lt;Error&gt;成功&lt;/Error&gt;&lt;ReservationDate&gt;2025-06-17&lt;/ReservationDate&gt;&lt;WeekName&gt;星期二&lt;/WeekName&gt;&lt;TotalAmount&gt;30&lt;/TotalAmount&gt;&lt;RemainAmount&gt;30&lt;/RemainAmount&gt;&lt;AppointmentAmount&gt;0&lt;/AppointmentAmount&gt;&lt;Shift /&gt;&lt;department_code&gt;3003&lt;/department_code&gt;&lt;department_name&gt;儿内科(前兴专家)&lt;/department_name&gt;&lt;doctor_code&gt;6927&lt;/doctor_code&gt;&lt;doctor_name&gt;李艳芳&lt;/doctor_name&gt;&lt;doctor_level&gt;副主任医师&lt;/doctor_level&gt;&lt;schedule_id&gt;2025-06-17||3003|6927|&lt;/schedule_id&gt;&lt;schedule_status&gt;1&lt;/schedule_status&gt;&lt;register_name&gt;副主任医师&lt;/register_name&gt;&lt;/DataTable&gt;&lt;/root&gt;</CallInterfaceResult></CallInterfaceResponse></soap:Body></soap:Envelope>
[ system ] 获取医生列表{"DataTable":[{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-13","WeekName":"\u661f\u671f\u4e94","TotalAmount":"65","RemainAmount":"0","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"0230","doctor_name":"\u4faf\u7476","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-13||3003|0230|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-14","WeekName":"\u661f\u671f\u516d","TotalAmount":"30","RemainAmount":"30","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"2296","doctor_name":"\u5b8b\u6625\u8273","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-14||3003|2296|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-14","WeekName":"\u661f\u671f\u516d","TotalAmount":"40","RemainAmount":"25","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"4120","doctor_name":"\u8096\u7956\u521a","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-14||3003|4120|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-15","WeekName":"\u661f\u671f\u65e5","TotalAmount":"30","RemainAmount":"29","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"540","doctor_name":"\u96f7\u5e86\u9f84","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-15||3003|540|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-16","WeekName":"\u661f\u671f\u4e00","TotalAmount":"30","RemainAmount":"30","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"6927","doctor_name":"\u674e\u8273\u82b3","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-16||3003|6927|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-17","WeekName":"\u661f\u671f\u4e8c","TotalAmount":"45","RemainAmount":"43","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"0230","doctor_name":"\u4faf\u7476","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-17||3003|0230|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-17","WeekName":"\u661f\u671f\u4e8c","TotalAmount":"40","RemainAmount":"40","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"4120","doctor_name":"\u8096\u7956\u521a","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-17||3003|4120|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"},{"Result":"1","Error":"\u6210\u529f","ReservationDate":"2025-06-17","WeekName":"\u661f\u671f\u4e8c","TotalAmount":"30","RemainAmount":"30","AppointmentAmount":"0","Shift":[],"department_code":"3003","department_name":"\u513f\u5185\u79d1(\u524d\u5174\u4e13\u5bb6)","doctor_code":"6927","doctor_name":"\u674e\u8273\u82b3","doctor_level":"\u526f\u4e3b\u4efb\u533b\u5e08","schedule_id":"2025-06-17||3003|6927|","schedule_status":"1","register_name":"\u526f\u4e3b\u4efb\u533b\u5e08"}]}
