<?php

// +----------------------------------------------------------------------
// | 滇医通
// +----------------------------------------------------------------------
// | Copyright (c) 2017 http://www.ynhdkc.com All rights reserved.
// +----------------------------------------------------------------------
// | Author: 黄生俊 <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\AppointRecord;
use app\lib\A2Xml;
use DateTime;
use EasyWeChat\Foundation\Application;
use pinyin\Pinyin;
use think\Db;
use think\Loader;
use app\common\service\HosService;
class RefactorReg extends Api
{

    protected $checkRegisterUrl = "http://220.165.247.114:8022/Content/KMSETYYWebService.asmx?wsdl";
    protected $checkRegisterUrlServer = "http://220.165.247.114:8022/Content/KMSETYYWebService.asmx";
    private $hospitalCode = [
        '871002' => 1,
        '871003' => 2,
        '871006' => 3
    ];
    private $accessKey = "95B94EDB1A0E0B20F603";
    private $organization = "1";

    private $specialDepartList1 = [
        ["hospital_code"=>"871002","department_code"=>"0231518","department_name"=>"特需门诊","department_notify"=>"<div>尊敬的家长：<p>1、请按预约时间就诊，积极配合工作人员分诊，迟到30分钟，系统将自动取消预约号。</p ><p>2、请遵守<span style='color: red'>“一人一陪护”</span>制度，为了您和他人的健康，请佩戴好口罩。</p >
<p>3、就诊过程中保持安静，请勿大声喧哗。</p ><p>4、特需诊查费：<span style='color: red'>300元/人次</span>。 </p ><p style='text-align:right;'>地址：前兴院区门诊三楼特需门诊。 </p >
<p style='text-align:right;'>咨询电话：0871-63309088\\63309188</p ></div>"],
        ["hospital_code"=>"871002","department_code"=>"A000282","department_name"=>"特需门诊体检","department_notify"=>"<div>尊敬的家长：<p>1、请按预约时间就诊，积极配合工作人员分诊，迟到30分钟，系统将自动取消预约号。</p ><p>2、请遵守<span style='color: red'>“一人一陪护”</span>制度，为了您和他人的健康，请佩戴好口罩。</p >
                                                                                            <p>3、就诊过程中保持安静，请勿大声喧哗。</p ><p>4、特需诊查费：<span style='color: red'>300元/人次</span>。 </p ><p style='text-align:right;'>地址：前兴院区门诊三楼特需门诊。 </p >
                                                                                            <p style='text-align:right;'>咨询电话：0871-63309088\\63309188</p ></div>"],
    ];

    private $specialDepartList2= [
        ["hospital_code"=>"871002","department_code"=>"A000158","department_name"=>"罕见病疑难病多学科联合门诊","department_notify"=>""],
        ["hospital_code"=>"871002","department_code"=>"A000280","department_name"=>"神经纤维瘤病多学科联合门诊","department_notify"=>""],
        ["hospital_code"=>"871002","department_code"=>"A000281","department_name"=>"难治性癫痫多学科联合门诊","department_notify"=>""],
        ["hospital_code"=>"871003","department_code"=>"A000303","department_name"=>"体重管理多学科联合门诊","department_notify"=>""],
    ];


    // type 1 特需  2 多学科
    public function special_depart_list($type=1) {
        $list = [];
        switch ($type) {
            case 1:
                $list = $this->specialDepartList1;
                break;
            case 2:
                $list = $this->specialDepartList2;
                break;
        }
        return $this->returnSuccess($list);

    }

    public function sync_doctor($department_code, $doctor_code = '') {
        /*$tmpKey = empty($doctor_code) ? '' : ':'.$doctor_code;
        $cacheKeyDepartment = "doctor:sync:depart_".$department_code.$tmpKey;
        $cacheKeyDepartmentInfo = cache($cacheKeyDepartment);
        if($cacheKeyDepartmentInfo) return $this->returnErorr($cacheKeyDepartment." 今天已经同步过");
        cache($cacheKeyDepartment,1,86400);*/


        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DoctorCode'] = empty($doctor_code) ? '' : $doctor_code;
        $params['department_code'] = $department_code;
        $res = $this->callCheckRegister('BK_GetDoctorList', $params);
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }

        $doctorCodeArray = array_column($result, 'DoctCode');
//        dump($doctorCodeArray);

        $hisDoctorList = array_column($result, null,'DoctCode');
//        dump($hisDoctorList);die();

        $dbDoctorList = Db::name('doctor')->distinct(true)->where(['doc_id'=>['in',$doctorCodeArray]])->column('doc_id,doc_avatar,doc_name');
//        dump($dbDoctorList);die();

        $dbDoctorListCodes = array_keys($dbDoctorList);
//        dump($dbDoctorListCodes);die();

        foreach ($dbDoctorListCodes as $value) {
            /*$cacheKey = "doctor:sync:doctor_id_".$value;
            $cacheInfo = cache($cacheKey);
            if($cacheInfo) continue;
            cache($cacheKey,1,43200);*/

            $where = ['doc_id' => $value];
            $updateData = [];
            if(!empty($hisDoctorList[$value]['doctor_level']))$updateData['title_name'] = $hisDoctorList[$value]['doctor_level'];
            if(!empty($hisDoctorList[$value]['doctor_good_at'])) $updateData['doc_good'] = $hisDoctorList[$value]['doctor_good_at'];
            if(!empty($hisDoctorList[$value]['DoctDesc'])) $updateData['doc_info'] = $hisDoctorList[$value]['DoctDesc'];
            if(!empty($hisDoctorList[$value]['DoctPic']) && empty($dbDoctorList[$value]['doc_avatar']))$updateData['doc_avatar'] = $hisDoctorList[$value]['DoctPic'];
//            dump($where);
//            dump($updateData);
            if(empty($updateData)) continue;
            Db::name('doctor')->where($where)->update($updateData);
        }

        return $this->returnSuccess([],"同步成功");
    }

    public function sync_depart_list($hospital_code) {
        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hospital_code];
        $res = $this->callCheckRegister('BK_GetOrgDept', $params);
//        dump($res);
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }
        $manString = "满";
        foreach ($result as $value) {
            if(!empty($value['department_notify']) && $value['department_notify'] != $manString) {
                Db::name('department')->where(['department_code' => $value['DeptSn']])->update(['department_notify' => $value['department_notify']]);
                $params = request()->param();
                if(isset($params['debug']) ) {
                    dump($value);
                }
            }
        }
//        dump($result);



    }


    public function get_depart_list($hospital_code)
    {
        $syncNotifyKey = 'department:sync:'.$hospital_code;
        $cacheInfo = cache($syncNotifyKey);
        if(!$cacheInfo) {
            try {
                $this->sync_depart_list($hospital_code);
            } catch (\Exception $e) {
            }
            cache($syncNotifyKey,1,60*30);
        }


        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hospital_code];
        $res = $this->callCheckRegister('BK_GetOrgDept', $params);
//        dump($res);
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }

//        dump($result);die();
        $filterDepartmentList = [];
        $departNotInDbList = [];

        $departs = Db::name('department')->where(['hospital_code' => $hospital_code])->select();
        // 这个里面是父级编码+科室编码

        $uniDepartIds = [];
        foreach ($departs as $value) {
            array_push($uniDepartIds,$value['parent_code'] . $value['department_code']);
        }


//        dump($departs);die();
        // 获取 dep_id 组成一维数组
//        $depIds = array_column($departs, 'department_code');
        $py = new Pinyin();
        $dateTime = date('Y-m-d H:i:s', time());
        $hasTodaySchedule = [];
        foreach ($result as $value) {
//            if ($value['department_status'] != 1 ) continue;
            if(empty($value['parent_code'])) $value['parent_code'] = '';
            $tmp = [];
            $tmp['department_code'] = $value['DeptSn'];
            $tmp['department_name'] = $value['DeptName'];
            $tmp['department_address'] = empty($value['department_address']) ? "" : $value['department_address'];
            $tmp['department_notify'] = empty($value['department_notify']) ? "" : $value['department_notify'];
            $tmp['parent_code'] = $value['parent_code'];
            array_push($filterDepartmentList, $tmp);
            $hasTodaySchedule[$tmp['department_code']] = empty($value['today_status']) ? 0 : 1;

            if(empty($value['parent_code'])) {
                if (!in_array($tmp['department_code'], $uniDepartIds)) {
                    array_push($uniDepartIds, $tmp['department_code']);
                    $insertData = [];
                    $insertData['hospital_code'] = $hospital_code;
                    $insertData['parent_code'] = '';
                    $insertData['department_code'] = $tmp['department_code'];
                    $insertData['department_name'] = $tmp['department_name'];
                    $insertData['department_sort'] = 0;
                    $char = strtoupper($py->getQp($insertData['department_name']));
                    $insertData['department_pinyin'] = $char;
                    $insertData['department_address'] = $tmp['department_address'];
                    $insertData['department_notify'] = $tmp['department_notify'];
                    $insertData['create_time'] = $dateTime;
                    $insertData['update_time'] = $dateTime;
                    $insertData['status'] = 1;
                    array_push($departNotInDbList, $insertData);
                }
            } else {
                $arr = explode(',', $tmp['parent_code']);
                foreach ($arr as $item) {
                    $tmpUniCode = $item.$tmp['department_code'];
                    if (!in_array($tmpUniCode, $uniDepartIds)) {
                        array_push($uniDepartIds, $tmpUniCode);
                        $insertData = [];
                        $insertData['hospital_code'] = $hospital_code;
                        $insertData['parent_code'] = $item;
                        $insertData['department_code'] = $tmp['department_code'];
                        $insertData['department_name'] = $tmp['department_name'];
                        $insertData['department_sort'] = 0;
                        $char = strtoupper($py->getQp($insertData['department_name']));
                        $insertData['department_pinyin'] = $char;
                        $insertData['department_address'] = $tmp['department_address'];
                        $insertData['department_notify'] = $tmp['department_notify'];
                        $insertData['create_time'] = $dateTime;
                        $insertData['update_time'] = $dateTime;
                        $insertData['status'] = 1;
                        array_push($departNotInDbList, $insertData);
                    }
                }
            }
        }

        if (count($departNotInDbList)) {
            Db::name('department')->strict(false)->insertAll($departNotInDbList); // 关闭字段严格检查,将数据添加到数据库
        }

        $departsDb = Db::name('department')->where(['hospital_code' => $hospital_code,'status' => 1])->order('department_sort desc')->select();
        foreach ($departsDb as &$item) {
            $item['has_today_schedule'] = isset($hasTodaySchedule[$item['department_code']])?$hasTodaySchedule[$item['department_code']]:0;
        }
        unset($item);

//        dump($departsDb);die();

        $tree = $this->covertMultiTree($departsDb);
//        dump($tree);
        return $this->returnSuccess($tree);
    }

    private function covertMultiTree($list,$parentCode = "") {
        $tree = [];
        foreach ($list as $item) {
            if ($item['parent_code'] === $parentCode) {
                $children = $this->covertMultiTree($list, $item['department_code']);
                if ($children) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }



    public function get_department_list($hospital_code)
    {

        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hospital_code];
        $res = $this->callCheckRegister('BK_GetOrgDept', $params);
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }

//        dump($result);die();
        $filterDepartmentList = [];
        $departNotInDbList = [];

        $departs = Db::name('Depart')->where(['hos_id' => $hospital_code])->select();
        // 获取 dep_id 组成一维数组
        $depIds = array_column($departs, 'dep_id');
        $py = new Pinyin();
        foreach ($result as $value) {
            if ($value['department_status'] != 1 || empty($value['parent_code'])) continue;
            $tmp = [];
            $tmp['department_code'] = $value['DeptSn'];
            $tmp['department_name'] = $value['DeptName'];
            $tmp['department_address'] = empty($value['department_address']) ? "" : $value['department_address'];
            $tmp['department_notify'] = empty($value['department_notify']) ? "" : $value['department_notify'];
            $tmp['parent_code'] = $value['parent_code'];
            array_push($filterDepartmentList, $tmp);

            if (!in_array($tmp['department_code'], $depIds)) {
                array_push($depIds, $tmp['department_code']);
                $insertData = [];
                $insertData['category_id'] = 0;
                $insertData['hos_id'] = $hospital_code;
                $insertData['dep_id'] = $tmp['department_code'];
                $insertData['dep_name'] = $tmp['department_name'];
                $insertData['dep_addr'] = $tmp['department_address'];
                $char = strtoupper($py->getJp($insertData['dep_name']));
                $insertData['first_char'] = $char;
                $insertData['sort'] = 0;
                $insertData['is_recomm'] = 0;
                $insertData['status'] = 0;

                array_push($departNotInDbList, $insertData);

            }
        }

        if (count($departNotInDbList)) {
            Db::name('Depart')->strict(false)->insertAll($departNotInDbList); // 关闭字段严格检查,将数据添加到数据库
        }
        $tree = $this->convertToTree($filterDepartmentList);
//        dump($tree);
        return $this->returnSuccess($tree);
    }

    private $needLogMethod = ["BK_DoBooking","BK_GetScheduleNew"];

    public function callCheckRegister($method, $params = array())
    {
        $request['root'] = $params;
        $requestBodyXml = $this->getXml($request);
        $request = $this->postXmlString($method, $requestBodyXml);
//        dump($request);
        if(in_array($method, $this->needLogMethod)){
            system_log($method."请求入参:" . $request);
        }
        $res = $this->sendCurlPostRequest($this->checkRegisterUrlServer, $request);
        if(in_array($method, $this->needLogMethod)){
            system_log($method."请求出参:" . $res['response']);
        }
//        dump($res);die();
        $res = $res['response'];
//        dump($res);die();

        $p = xml_parser_create();
        xml_parse_into_struct($p, $res, $vals, $index);
        xml_parser_free($p);
//        dump($vals);dump($index);die;
        if (!isset($index['CALLINTERFACERESULT'])) {
            // 数据错误
            return [];
        }
        $k = $index['CALLINTERFACERESULT'][0];
        $bodyXml = $vals[$k]['value'];
        $resultXml = simplexml_load_string($bodyXml); // 并将字符串转换成xml
        $response = json_decode(json_encode($resultXml), TRUE);
        return $response;
    }

    private function getXml($params)
    {
        //拼装xml
        $xml = new A2Xml();
        $requsterXml = $xml->toXml($params);
//        dump($requsterXml);die;
        return $requsterXml;
    }

    private function postXmlString($method, $xmlString)
    {
        $xmlString = <<<EOF
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:tem="http://tempuri.org/">
    <soapenv:Header/>
    <soapenv:Body>
        <tem:CallInterface>
            <!--Optional:-->
            <tem:serviceId><![CDATA[$method]]></tem:serviceId>
            <!--Optional:-->
            <tem:msgBody><![CDATA[$xmlString]]>
            </tem:msgBody>
        </tem:CallInterface>
    </soapenv:Body>
</soapenv:Envelope>
EOF;
        return $xmlString;
    }

    private function sendCurlPostRequest($url, $xmlData, $timeout = 30)
    {
        // 初始化 cURL
        $ch = curl_init();

        // 设置请求 URL
        curl_setopt($ch, CURLOPT_URL, $url);

        // 设置请求方法为 POST
        curl_setopt($ch, CURLOPT_POST, 1);

        // 设置 POST 数据
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xmlData);

        // 设置请求头
        $headers = [
            'Content-Type: text/xml;charset=UTF-8'
        ];
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        // 设置超时时间
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);

        // 接收响应内容
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // 执行请求
        $response = curl_exec($ch);
        $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        // 关闭 cURL
        curl_close($ch);

        // 返回结果数组
        return [
            'status_code' => $statusCode,
            'response' => $response,
            'error' => $error
        ];
    }

    function convertToTree($departments)
    {
        $departmentMap = [];
        $rootDepartments = [];

        // 首先将所有部门放入 Map 中
        foreach ($departments as $department) {
            $departmentMap[$department['department_code']] = $department;
        }

        // 构建树形结构
        foreach ($departments as $department) {
            $parentCode = $department['parent_code'];
            if ($parentCode === null || !isset($departmentMap[$parentCode])) {
                // 如果没有父部门或者父部门不存在，将其作为根部门
                $rootDepartments[] = $department;
            } else {
                // 否则，将其添加到父部门的子部门列表中
                $parent = $departmentMap[$parentCode];
                if (!isset($parent['children'])) {
                    $parent['children'] = [];
                }
                $parent['children'][] = $department;
                $departmentMap[$parentCode] = $parent;
            }
        }

        return $rootDepartments;
    }


    public function get_pro_doctor_list($hos_code){
        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hos_code];
        $res = $this->callCheckRegister('BK_GetProSchedule', $params);
//        dump($res);die();
        if (empty($res)) return $this->returnErorr("获取专家医生列表失败");
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }

    }

    public function doctorList($hos_code, $dep_id, $from_date = '', $end_date = '')
    {
        $departKey = sprintf("depart:info:%s", $dep_id);
        $department = cache($departKey);
        if(empty($department)) {
            $department = Db::name('department')->where(['department_code' => $dep_id])->find();
            if (empty($department)) return $this->returnErorr("由于缺失科室信息获取医生列表失败");
            cache($departKey, $department, 3600 * 3);
            try {
                $this->sync_doctor($dep_id);
            } catch (\Exception $e) {
            }
        }
//        dump($department);

        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hos_code];
        $params['DeptSn'] = $dep_id;
        $params['DoctCode'] = "";
        $res = $this->callCheckRegister('BK_GetScheduleNew', $params);
//        dump($res);die();
        system_log("获取医生列表" . json_encode($res));
        if (empty($res)) return $this->returnErorr("获取医生列表失败");
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }


        $filterDoctorList = [];
        $doctorNotInDbList = [];

        $doctors = Db::name('doctor')->where(['hos_id' => $hos_code, 'dep_id' => $dep_id])->select();
        // 获取 dep_id 组成一维数组
        $docIds = array_column($doctors, 'doc_id');
        $doctorsDb = array_column($doctors, null, 'doc_id');
        $time = time();
        foreach ($result as &$value) {
            if (empty($value['department_code'])) $value['department_code'] = $dep_id;
            if (empty($value['department_name'])) $value['department_name'] = $department['department_name'];
            if (empty($value['doctor_code'])) $value['doctor_code'] = $value['department_code'];
            if (empty($value['doctor_name'])) $value['doctor_name'] = $value['department_name'];
            if (empty($value['doctor_level'])) $value['doctor_level'] = '';
            $value['hos_id'] = $hos_code;
            if (!in_array($value['doctor_code'], $docIds, true)) {
                array_push($docIds, $value['doctor_code']);

                $tmp = [];
                $tmp['hos_id'] = $hos_code;
                $tmp['dep_id'] = $value['department_code'];
                $tmp['doc_id'] = $value['doctor_code'];
                $tmp['doc_name'] = $value['doctor_name'];
                $tmp['title_name'] = $value['doctor_level'];
                $tmp['level_name'] = $value['doctor_level'];
                $tmp['create_time'] = $time;
                $tmp['update_time'] = $time;
                $tmp['status'] = 1;
                system_log("医生列表数据" . json_encode($tmp));
                array_push($doctorNotInDbList, $tmp);

            }
            $value['doc_avatar'] = isset($doctorsDb[$value['doctor_code']]['doc_avatar']) ? $doctorsDb[$value['doctor_code']]['doc_avatar'] : '';

            unset($value['Result']);
            unset($value['Error']);

            array_push($filterDoctorList, $value);
        }
        unset($value);
        if (count($doctorNotInDbList)) {
            Db::name('doctor')->strict(false)->insertAll($doctorNotInDbList);
        }
        $res = $this->convertToFrontDoctorList($filterDoctorList);


        return $this->returnSuccess($res);
    }

    function convertToFrontDoctorList($doctorList)
    {
        if (empty($doctorList)) return [];
//        $current_date = new DateTime();

        $output = [
            "sch_date_list" => [],
            "doctor_list" => []
        ];

        $date_info = [];
        $doctor_info = [];

        $pinyin = new Pinyin();

        foreach ($doctorList as $entry) {
            // 提取关键字段
            $reservation_date = $entry["ReservationDate"];
            $week_name = $entry["WeekName"];
            $total_amount = intval($entry["TotalAmount"]);
            $remain_amount = intval($entry["RemainAmount"]);
            $department_code = $entry["department_code"];
            $department_name = $entry["department_name"];
            $doctor_code = $entry["doctor_code"];
            $doctor_name = $entry["doctor_name"];
            $doctor_name_pinyin = $pinyin->getQp($doctor_name);
            $doctor_level = $entry["doctor_level"];
            $schedule_status = $entry["schedule_status"];
            $doc_avatar = $entry["doc_avatar"];
            $register_name = $entry["register_name"];

            // 格式化日期信息
            $sch_date_short = substr($reservation_date, 5);
//            $reservation_datetime = new DateTime($reservation_date);
//            $is_tomorrow = $current_date->modify("+1 day")->format("Y-m-d") === $reservation_date;
//            $current_date->modify("-1 day"); // 恢复当前时间
            $week_display = $week_name;
            if ($reservation_date == date('Y-m-d')) {
                $week_display = "今天";
            } else if ($reservation_date == date('Y-m-d', strtotime('+1 day'))) {
                $week_display = "明天";
            }

            // 更新日期统计信息
            if (!isset($date_info[$reservation_date])) {
                $date_info[$reservation_date] = [
                    "sch_date" => $reservation_date,
                    "sch_date_short" => $sch_date_short,
                    "week" => $week_display,
                    "src_num" => 0
                ];
            }
            $date_info[$reservation_date]["src_num"] += $remain_amount;

            // 构造医生唯一键
            $key = "$department_code-$doctor_code";

            // 更新医生统计信息
            if (!isset($doctor_info[$key])) {
                $doctor_info[$key] = [
                    "hos_id" => $entry["hos_id"],
                    "dep_id" => $department_code,
                    "dep_name" => $department_name,
                    "doc_id" => $doctor_code,
                    "doc_name" => $doctor_name,
                    "doc_name_pinyin" => $doctor_name_pinyin,
                    "doc_avatar" => $doc_avatar,
                    "sch_date_list" => [],
                    "sch_date_status" => [],
                    "sch_date_short_list" => [],
                    "amt" => 0,
                    "level_name" => $doctor_level,
                    "register_name" => $register_name,
                    "src_num" => 0,
                    "SchDate" => []
                ];
            }

            // 更新医生的预约信息
            $doctor_info[$key]["amt"] += 0;
            $doctor_info[$key]["src_num"] += $remain_amount;

            // 添加到 sch_date_list 和 sch_date_short_list
            if (!in_array($reservation_date, $doctor_info[$key]["sch_date_list"])) {
                $doctor_info[$key]["sch_date_list"][] = $reservation_date;
                $doctor_info[$key]["sch_date_short_list"][] = $sch_date_short;
            }

            // 更新 sch_date_status
            if (!isset($doctor_info[$key]["sch_date_status"][$reservation_date])) {
                $doctor_info[$key]["sch_date_status"][$reservation_date] = [
                    "sch_date" => $reservation_date,
                    "src_num" => $remain_amount,
                    "book_status" => intval($schedule_status),
                    "book_flag" => 0
                ];
            } else {
                $doctor_info[$key]["sch_date_status"][$reservation_date]["src_num"] += $remain_amount;
            }

            // 更新 SchDate
            if (!isset($doctor_info[$key]["SchDate"][$reservation_date])) {
                $doctor_info[$key]["SchDate"][$reservation_date] = [
                    "SchDate" => $reservation_date,
                    "CanBookNum" => $remain_amount,
                    "BookStatus" => $schedule_status,
                    "BookFlag" => 0,
                    "BookFlagArray" => ["0", "0"]
                ];
            } else {
                $doctor_info[$key]["SchDate"][$reservation_date]["CanBookNum"] += $remain_amount;
            }
        }

        $output["sch_date_list"] = array_values($date_info);
        $output["doctor_list"] = array_values($doctor_info);
        return $output;
    }

    public function get_time_schedule_list($hospital_code, $department_code, $doctor_code = '', $expert_tag = 0)
    {

        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hospital_code];
        $params['DeptSn'] = $department_code;
        $params['DoctCode'] = $doctor_code;
        $res = $this->callCheckRegister('BK_GetSchedule', $params);
        if (empty($res)) return $this->returnErorr("获取医生列表失败");
        $result = [];
//        dump($res);die();
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }
        dump($result);
    }


    public function doctor_detail_and_schedule($hos_code, $dep_code, $doc_code, $from_date = '', $end_date = '')
    {

        $depart = Db::name('department')->where(['hospital_code' => $hos_code, 'department_code' => $dep_code])->find();
        if (empty($depart)) return $this->returnErorr("该科室不存在");

        $doctor = Loader::model('Doctor')->getDoctorDetailNew($hos_code,$dep_code,$doc_code);
        if (empty($doctor)) return $this->returnErorr("该医生不存在");
        if(strstr($doctor['doc_avatar'],'qyy.etyy.cn'))$doctor['doc_avatar'] = str_replace('https://ertong-public.ynhdkc.com','',$doctor['doc_avatar']);
//        dump($doctor);die();

        $resultData = $doctor;
        $resultData['doc_good'] = $resultData['doc_good'].' '.$resultData['doc_info'];
        $resultData['dep_name'] = $depart['department_name'];
//        $resultData['appointment_notice'] = '';
        $resultData['schedule'] = '';

        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hos_code];
        $params['DeptSn'] = in_array($dep_code,['Q02','S03']) ? '' : $dep_code;
        $params['DoctCode'] = $dep_code == $doc_code ? '' : $doc_code;
        $res = $this->callCheckRegister('BK_GetScheduleNew', $params);
        if (empty($res)) return $this->returnErorr("暂无可约排班");
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }

        $schedule = [];
//        dump($result);die();

        $weekArr = array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
        foreach ($result as $value) {
            $tmp = [];
            $schedule_id = $value['schedule_id'];
            $tmp['schedule_id'] = $value['schedule_id'];
            $tmp['book_enable'] = $value['schedule_status'] = 1 ? true : false;
            $tmp['sch_date'] = $value['ReservationDate'];
            $tmp['src_num'] = $value['RemainAmount'];
            $tmp['register_name'] = $value['register_name'];
//            $tmp['time_type'] = $this->timeTypeToDyt($value['Shift']);
//            $tmp['time_type_desc'] = $this->timeTypeToStr($tmp['time_type']);
            $tmp['week'] = '未知';
            if ($value['ReservationDate'] == date('Y-m-d')) {
                $tmp['week'] = "今天";
            } else if ($value['ReservationDate'] == date('Y-m-d', strtotime('+1 day'))) {
                $tmp['week'] = "明天";
            } else {
                $tmp['week'] = $weekArr[date("w", strtotime($value['ReservationDate']))];
            }
//            $tmp['partSchedule'] = $this->fromHisGetTimeSchedule($schedule_id);

            array_push($schedule, $tmp);
        }
        $resultData['schedule'] = $schedule;

        return $this->returnSuccess($resultData);

    }


    public function doctorDetail($hos_code, $dep_id, $doc_id, $from_date = '', $end_date = '')
    {

        $depart = Db::name('Depart')->where(['hos_id' => $hos_code, 'dep_id' => $dep_id])->find();
        if (empty($depart)) return $this->returnErorr("该科室不存在");

        $doctor = Loader::model('Doctor')->getDoctorDetail($hos_code,$dep_id,$doc_id);
        if (empty($doctor)) return $this->returnErorr("该医生不存在");

        $resultData = $doctor;
        $resultData['dep_name'] = $depart['dep_name'];
        $resultData['appointment_notice'] = '';
        $resultData['schedule'] = '';
//        dump($resultData);die();

        $list = $this->fromHisGetSchedule($hos_code, $dep_id, $doc_id);

        $schedule = [];

        $weekArr = array("星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六");
        foreach ($list as $value) {
            $tmp = [];
            $schedule_id = $value['schedule_id'];
            $tmp['schedule_id'] = $value['schedule_id'];
            $tmp['book_enable'] = $value['schedule_status'] = 1 ? true : false;
            $tmp['sch_date'] = $value['ReservationDate'];
            $tmp['src_num'] = $value['RemainAmount'];
            $tmp['time_type'] = $this->timeTypeToDyt($value['Shift']);
            $tmp['time_type_desc'] = $this->timeTypeToStr($tmp['time_type']);
            $tmp['week'] = $value['ReservationDate'];
            if ($value['ReservationDate'] == date('Y-m-d')) {
                $tmp['week'] = "今天";
            } else if ($value['ReservationDate'] == date('Y-m-d', strtotime('+1 day'))) {
                $tmp['week'] = "明天";
            } else {
                $tmp['week'] = $weekArr[date("w", strtotime($value['ReservationDate']))];
            }
            $tmp['partSchedule'] = $this->fromHisGetTimeSchedule($schedule_id);

            $schedule['sch'.$schedule_id] = $tmp;
        }
        $resultData['schedule'] = $schedule;

        return $this->returnSuccess($resultData);

    }

    public function fromHisGetSchedule($hos_code, $dep_id, $doc_id)
    {
        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptNo'] = $this->hospitalCode[$hos_code];
        $params['DeptSn'] = $dep_id;
        $params['DoctCode'] = $dep_id == $doc_id ? '' : $doc_id;


        $res = $this->callCheckRegister('BK_GetSchedule', $params);
        if (empty($res)) return $this->returnErorr("医生排班失败");
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }
        return $result;
    }

    function timeTypeToDyt($timeType)
    {
        // 1 上午；2 中午；3 下午；4 晚上 => 1上午,2下午,3中午,4晚上
        switch ($timeType) {
            case 'a' :
                return 1;
            case 'm' :
                return 2;
            case 'p' :
                return 3;
            case 'n' :
                return 4;
            case 'all':
                return 7;
            default:
                return 0;
        }
    }

    function timeTypeToStr($timeType)
    {
        $data = ['1' => '上午', '2' => '中午', '3' => '下午', '4' => '晚上', '7' => '全天', '0' => '未知'];
        return $data[$timeType];
    }

    public function fromHisGetTimeSchedule($schedule_id)
    {
        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptSn'] = "";
        $params['DoctCode'] = "";
        $params['Shift'] = "";
        $params['schedule_id'] = $schedule_id;
        $res = $this->callCheckRegister('BK_GetTimeSchedule', $params);
        if (empty($res)) return [];
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }
        $timeScheduleList = [];
        foreach ($result as $value) {
            $tmp = [];
            $srcNum = 0;
            if(!empty($value['QueueSn'])) $srcNum = count(explode(',', $value['QueueSn']));
            $tmp['book_enable'] = $value['IsBooking'] == "False" ? true : false;
            $tmp['queue_sn'] = $value['QueueSn'];
            $tmp['sch_date'] = date('Y-m-d', strtotime($value['ReservationTime']));
            $tmp['src_num'] = $srcNum;// $tmp['book_enable'] == true ? 1 : 0;
            $tmp['start_time'] = date('H:i', strtotime($value['start_time']));
            $tmp['end_time'] = date('H:i', strtotime($value['end_time']));
            array_push($timeScheduleList, $tmp);
        }
        return $timeScheduleList;

    }


    public function get_time_schedule($schedule_id)
    {
        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['DeptSn'] = "";
        $params['DoctCode'] = "";
        $params['Shift'] = "";
        $params['schedule_id'] = $schedule_id;
        $res = $this->callCheckRegister('BK_GetTimeSchedule', $params);
//        dump($res);die;
        if (empty($res)) return $this->returnErorr("获取失败");
        $result = [];
        if (isset($res['DataTable'][0])) {
            $result = $res['DataTable'];
        } else {
            $result[0] = $res['DataTable'];
        }
        $timeScheduleList = [];
        foreach ($result as $value) {
            $tmp = [];
            $srcNum = 0;
            if(!empty($value['QueueSn'])) $srcNum = count(explode(',', $value['QueueSn']));
            $tmp['book_enable'] = $value['IsBooking'] == "False" ? true : false;
            $tmp['queue_sn'] = $value['QueueSn'];
            $tmp['sch_date'] = date('Y-m-d', strtotime($value['ReservationTime']));
            $tmp['src_num'] = $srcNum;// $tmp['book_enable'] == true ? 1 : 0;
            $tmp['start_time'] = date('H:i', strtotime($value['start_time']));
            $tmp['end_time'] = date('H:i', strtotime($value['end_time']));
            $tmp['schedule_id'] = $value['Schedule_id'];
            array_push($timeScheduleList, $tmp);
        }
        if(empty($timeScheduleList)) return $this->returnErorr("预约失败");;
        return $this->returnSuccess($timeScheduleList);

    }

    public function submit()
    {
        debug('submitOrderStart');
        $request = $this->request;
        $userid = $request->param('userid', 0);
        $openid = $request->param('openid', 0);
        $userinfo = $this->getUserInfo($userid, $openid);
        if (!$userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        $params = $request->param();
        if (empty($params['hos_code'])) {
            return $this->returnErorr("缺少医院参数");
        }
        if (empty($params['dep_id'])) {
            return $this->returnErorr("缺少科室参数");
        }
        if (!isset($params['doc_id'])) {
            return $this->returnErorr("缺少医生参数");
        }
        if (empty($params['pat_id'])) {
            return $this->returnErorr("缺少患者参数");
        }
        if (!isset($params['schedule_id'])) {
            return $this->returnErorr("缺少预约号源参数");
        }
        if (empty($params['sch_date'])) {
            return $this->returnErorr("缺少就诊日期参数");
        }
        if (empty($params['time_type'])) {
            return $this->returnErorr("缺少就诊时间类型参数");
        }
        if (empty($params['queue_sn'])) {
            return $this->returnErorr("缺少就诊排班序号");
        }
        $patient_detail = Loader::model('Patient')->getDetail($params['pat_id']);
        if (empty($patient_detail)) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        if (!isset($patient_detail['user_id']) || $userid != $patient_detail['user_id']) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        Loader::model('Patient')->setPatientToDefault($patient_detail['id'], $userid);
        if(empty($patient_detail['patient_birthday'])) return $this->returnErorr("缺少出生日期无法预约", 99);
        if(empty($patient_detail['patient_sex'])) return $this->returnErorr("缺少性别信息无法预约", 99);
        if(empty($patient_detail['patient_name'])) return $this->returnErorr("缺少姓名信息无法预约", 99);



                $hosService = new HosService('871002');
                // 查询his中是否已存在相同的就诊人
                // 通过就诊人就诊卡，调用HIS接口BK_GetPatInfo
                $result = $hosService->searchPatientCardByJzCard(['jz_card' => $patient_detail['jz_card']]);

                if ($result['code'] == 1 && $result['data']) {
                    $patientInfoFromHis = $result['data']; // 返回数0组
                    $needupdate = false;
                   //判断姓名、性别、证件号是否一致，如果不一致，则更新数据库

                    system_log("----------------------------------------------------------------------------------");
                    system_log('挂号患者信息校验-his：'.json_encode($patientInfoFromHis));
                    system_log('挂号患者信息校验-db：'.json_encode($patient_detail));
                    system_log("----------------------------------------------------------------------------------");
                   if(!empty($patientInfoFromHis['patient_name'])&& ($patientInfoFromHis['patient_name'] != $patient_detail['patient_name'])){
                       $patient_detail['patient_name'] = $patientInfoFromHis['patient_name'];
                       $needupdate = true;
                    }
                    if(!empty($patientInfoFromHis['patient_birthday']) && ($patientInfoFromHis['patient_birthday'] != $patient_detail['patient_birthday'])){
                        $patient_detail['patient_birthday'] = $patientInfoFromHis['patient_birthday'];
                        $needupdate = true;
                    }
                    if(!empty($patientInfoFromHis['patient_sex']) &&($patientInfoFromHis['patient_sex'] != $patient_detail['patient_sex'])){
                        $patient_detail['patient_sex'] = $patientInfoFromHis['patient_sex'];
                        $needupdate = true;
                    }
                    if(!empty($patientInfoFromHis['patient_idcard']) &&($patientInfoFromHis['patient_idcard'] != $patient_detail['patient_idcard'])){
                        $patient_detail['patient_idcard'] = $patientInfoFromHis['patient_idcard'];
                        $needupdate = true;
                    }
                    $patientList = Loader::model('Patient')->update($patient_detail);

                }

        $params['jz_card'] = $patient_detail['jz_card'];
        $params['user_id'] = $patient_detail['user_id'];
        $params['patient_name'] = $patient_detail['patient_name'];
        $params['patient_phone'] = $patient_detail['patient_phone'];
        $params['patient_relation'] = $patient_detail['patient_relation'];
        $params['patient_sex'] = $patient_detail['patient_sex'];
        $params['patient_birthday'] = $patient_detail['patient_birthday'];
        $params['guarantee_name'] = $patient_detail['guarantee_name'];
        $params['patient_idcard'] = $patient_detail['patient_idcard'];
        $params['guarantee_idcard'] = $patient_detail['guarantee_idcard'];
        $doctor_info = Loader::model('Doctor')->getDoctorDetailNew($params['hos_code'], $params['dep_id'], $params['doc_id']);
        if ($doctor_info) {
            $params['hos_name'] = $doctor_info['hos_name'];
            $params['dep_name'] = $doctor_info['dep_name'];
            $params['doc_name'] = $doctor_info['doc_name'];
            $params['doc_level'] = $doctor_info['title_name'] ? $doctor_info['title_name'] : $doctor_info['level_name'];
        } else {
            $depart_info = Loader::model('Depart')->getDetail($params['hos_code'], $params['dep_id']);
            if (empty($hos_info)) {
                return $this->returnErorr("系统正忙");
            }
            $params['dep_name'] = $depart_info['dep_name'];

            $hos_info = Loader::model('Hospital')->getDetail($params['hos_code']);
            if (empty($hos_info)) {
                return $this->returnErorr("系统正忙");
            }
            $params['hos_name'] = $hos_info['hospital_name'];

        }

        $appointModel = model('AppointRecord');
       /* $record_id = $appointModel->where(['user_id' => $params['user_id'], 'jz_card' => $params['jz_card'], 'dep_id' => $params['dep_id'], 'sch_date' => $params['sch_date'], 'status' => 1])->value('id');
        if ($record_id) {
            return $this->returnErorr('同一科室一天只能预约一个号');
        }*/

        $order_no = self::genOrderNo($prefix = 'GH');
        $appointRecord['order_no'] = $order_no;
        $appointRecord['user_id'] = $params['user_id'];
        $appointRecord['patient_id'] = $params['pat_id'];
        $appointRecord['jz_card'] = $params['jz_card'];
        $appointRecord['patient_idcard'] = isset($params['patient_idcard']) ? $params['patient_idcard'] : '';
        $appointRecord['patient_name'] = $params['patient_name'];
        $appointRecord['patient_phone'] = $params['patient_phone'];
        $appointRecord['hos_id'] = $params['hos_code'];
        $appointRecord['dep_id'] = $params['dep_id'];
        $appointRecord['doc_id'] = $params['doc_id'];
        $appointRecord['hos_name'] = isset($params['hos_name']) ? $params['hos_name'] : "";
        $appointRecord['dep_name'] = isset($params['dep_name']) ? $params['dep_name'] : "";
        $appointRecord['doc_name'] = isset($params['doc_name']) ? $params['doc_name'] : "";
        $appointRecord['doc_level'] = isset($params['title_name']) ? $params['title_name'] : "";
        $appointRecord['schedule_id'] = $params['schedule_id'];
        $appointRecord['sch_date'] = $params['sch_date'];
        $appointRecord['time_type'] = $params['time_type'];
        $appointRecord['his_status'] = 1;

        $appointRecord['amt'] = isset($options['amt']) ? $options['amt'] : 0; // 缴费金额
        $appointRecord['msg_content'] = '';
        $appointRecord['pay_status'] = -1; // -2已退款-1 不需要支付0 待支付 1 已支付
        $appointRecord['create_time'] = time(); // 创建时间
        $appointRecord['update_time'] = time(); // 创建时间
        $appointRecord['status'] = 0; // -1已撤销 1 预约成功 0 待生成承诺书
        $appointRecord['pre_outpatient'] = 0;//设置预问诊标识为未进行预问诊


        // 预约接口的字段bookingtype传值{5=微信公众号，6=滇医通，7=支付宝}
        $requestParams['AccessKey'] = $this->accessKey;
        $requestParams['Organization'] = $this->organization;
        $requestParams['DeptNo'] = $this->hospitalCode[$params['hos_code']];
        $requestParams['schedule_id'] = $params['schedule_id'];
        $requestParams['QueueSn'] = $params['queue_sn'];
        $requestParams['PatientID'] = $patient_detail['jz_card'];
        $requestParams['bookingtype'] = "5";
        $requestParams['order_no'] = $order_no;
        $requestParams['DeptSn'] = $params['dep_id'];
        $requestParams['CardNo'] = $patient_detail['jz_card'];
        $requestParams['Name'] = $params['patient_name'];
        $requestParams['patient_sex'] = $params['patient_sex'];
        $requestParams['Phone'] = $params['patient_phone'];
        $requestParams['IdNo'] = $params['patient_idcard'];
        $requestParams['patient_birthday'] = $params['patient_birthday'];

        $res = $this->callCheckRegister('BK_DoBooking', $requestParams);
        system_log("预约平台挂号返回的信息如下" . json_encode($res));
        if (empty($res)) {
            return $this->returnErorr("预约失败");
        }
        if ($res['DataTable']['Result'] != 1) {
            return $this->returnErorr($res['DataTable']['Error']);
        }

        $appointModel->data($appointRecord)->save();
        $recordIdInDb = $appointModel->id;


        $appointRecordUpdate['status'] = 1;
        $appointRecordUpdate['msg_content'] = $res['DataTable']['RoomAddress'];
        $appointRecordUpdate['hos_payed_no'] = $res['DataTable']['RegId'];
        $appointRecordUpdate['start_time'] = date('H:i',strtotime($res['DataTable']['BeginTime']));
        $appointRecordUpdate['end_time'] = date('H:i',strtotime($res['DataTable']['EndTime']));
        Db::name('appoint_record')->where(['id' => $recordIdInDb])->update($appointRecordUpdate);

        try {
            $this->sendTemplateMessage($params['patient_name'],$patient_detail['jz_card'],$params['pat_id'],$params['dep_name'],
                $params['doc_name'],$params['sch_date'],$appointRecordUpdate['start_time'],$recordIdInDb);
        } catch (\Exception $e) {

        }

        return $this->returnSuccess(['order_no' => $order_no, 'order_id' => $recordIdInDb, 'type' => 2, 'msg_content' => ""]);
    }


    public static function genOrderNo($prefix = 'WX')
    {
        return $prefix . date('ymd') . substr(time(), -6) . random_int(100, 999) . substr(microtime(), 2, 3);
    }


    public function cancel($userid, $openid, $id)
    {
        $userinfo = $this->getUserInfo($userid, $openid);
        if (!$userinfo) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }
        // 获取预约记录详情
        $AppointRecord = new AppointRecord();
        $appointRecordItem = $AppointRecord->getDetail($id);
        if (!$appointRecordItem) {
            return $this->returnErorr('未找到预约记录,或已取消');

        }
        if ($appointRecordItem['user_id'] != $userid) {
            return $this->returnErorr("用户认证失败，请重新授权", 99);
        }

        /*$jz_start_time = strtotime($appointRecordItem['sch_date'] . ' ' . $appointRecordItem['start_time']);
        if ($jz_start_time - time() < 0) {
            return $this->returnErorr("当前所选的登记信息不能取消，可能已分诊取号或预约通道已经关闭！");
        }*/

        if(empty($appointRecordItem['hos_payed_no'])) {
            $obj = new Appoint();
            return $obj->cancel($userid, $openid, $id);
            /*$appointRecordItem['hos_payed_no'] = Db::name('reg_middle')
                ->where(['jz_card'=> $appointRecordItem['jz_card'],'schedule_id' => $appointRecordItem['schedule_id']])
                ->value('hos_payed_no');*/
        }


        $params['AccessKey'] = $this->accessKey;
        $params['Organization'] = $this->organization;
        $params['RegId'] = $appointRecordItem['hos_payed_no'];

        $res = $this->callCheckRegister('BK_DoCancelBook', $params);
        system_log("预约平台退号返回的信息如下" . json_encode($res));
        if (empty($res)) {
            return $this->returnErorr("预约失败");
        }
        if ($res['DataTable']['Result'] != 1) {
            return $this->returnErorr($res['DataTable']['Error']);
        }

        $appointRecordUpdate['status'] = -1;

        Db::name('appoint_record')->where(['id' => $appointRecordItem['order_id']])->update($appointRecordUpdate);

        return $this->returnSuccess();
    }



    public function sendTemplateMessage($patientName, $jzCard, $patientId, $departName, $doctorName, $schDate, $startTime, $orderId){
        $tepParam = [];
        $tepParam['first'] = '恭喜您成功使用微信公众号进行预约挂号';
        // 就诊人
        $tepParam['keyword1'] = $patientName;
        // 患者ID号
        $tepParam['keyword2'] = $jzCard."(患者ID)";
        $openid = model('Patient')->getOpenid($patientId);
        // 挂号科室
        $tepParam['keyword3'] = $departName;
        // 看诊医生
        $tepParam['keyword4'] = $doctorName;
        // 就诊时间
        $tepParam['keyword5'] = $schDate . substr($startTime, 0, 5);
        $time = date('Y年m月d日H时i分',strtotime($tepParam['keyword5']));
        $tepParam['keyword5'] = $time;
        // 底部说明
        $tepParam['remark'] = "请在预约就诊时间前30分钟取号，过时取号就诊时间将后延。为了维护您和其他患者的利益，无需就诊，请您在所预约就诊时间前取消预约的订单。";
        $url = "https://ertong.ynhdkc.com/ui/pages/ucenter/appoint/detail?id=".$orderId;
        try {
            $app = new Application(config('wechat'));
            $notice = $app->notice;
            $messageId = $notice->send([
                'touser' => $openid,
                'template_id' => 'dA1h8UkE8d0F_-Me3rba6syvcR-IFF-CTO9a39pk1n0',
                'url' => $url,
                'data' => $tepParam,
            ]);
        } catch (\Exception $e) {
            system_log('发送微信模板消息失败：预约成功消息，to_openid:'.$openid);
        }
    }


}
